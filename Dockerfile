FROM golang:alpine
LABEL maintainer="tianming.song"

ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct

ENV PROJECT="fuse"

WORKDIR $GOPATH/src/$PROJECT
COPY . .

ARG BUILD_ENV="prod"

ARG MODULE="engine"

RUN go env -w GOINSECURE=gitlab.docsl.com
RUN go env -w GOPRIVATE=gitlab.docsl.com
RUN go install -v ./cmd/$MODULE && go build -v -o output/$PROJECT-$MODULE ./cmd/$MODULE
RUN mkdir -p output/logs
RUN cp -r conf/$BUILD_ENV.toml output/conf.toml


FROM golang:alpine
LABEL maintainer="tianming.song"

ENV GOPROXY=https://goproxy.cn,https://goproxy.io,direct

ENV PROJECT="fuse"

ARG MODULE="engine"

ENV MODULE=$MODULE

WORKDIR $GOPATH/src/$PROJECT
COPY --from=0 $GOPATH/src/$PROJECT/output ./output

CMD ["sh","-c","./output/$PROJECT-$MODULE -config ./output/conf.toml -logfile ./output/logs/$PROJECT-$MODULE.log"]
