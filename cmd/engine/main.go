/**
 * @note
 * main.go
 *
 * <AUTHOR>
 * @date 	2025-01-23
 */
package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"log"
	"os"

	"github.com/kataras/iris/v12"
	"github.com/tmsong/hlog"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/cas"
	"gitlab.docsl.com/security/common/http_server"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"gitlab.docsl.com/security/common/prometheus"
	"gitlab.docsl.com/security/common/redis"
	"gitlab.docsl.com/security/fuse/internal/handler/assets"
	engineHandler "gitlab.docsl.com/security/fuse/internal/handler/engine"
	"gitlab.docsl.com/security/fuse/internal/handler/rule"
	"gitlab.docsl.com/security/fuse/internal/handler/transaction"
	"gitlab.docsl.com/security/fuse/internal/handler/user"
	"gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/config"
	"gitlab.docsl.com/security/fuse/pkg/middleware"
)

/**
 * @note
 * 运行选项
 */
func Flagset() *flag.FlagSet {
	flagSet := flag.NewFlagSet(common.ModuleName, flag.ExitOnError)
	flagSet.String("config", "", "path to config file")
	flagSet.String("logfile", "", "log output file")
	flagSet.String("outputConfig", "", "output config file")
	flagSet.String("encrypt", "", "encrypt config file")

	return flagSet
}

func main() {
	////////////////////CONFIG LOAD////////////////////

	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("FUSE_CONFIG_CRYPT_KEY"))
	})

	//读取运行参数
	flagSet := Flagset()
	flagSet.Parse(os.Args[1:])

	//读取配置
	outputConfig := flagSet.Lookup("outputConfig").Value.String()
	encrypt := flagSet.Lookup("encrypt").Value.String() == "true"
	configFile := flagSet.Lookup("config").Value.String()
	if configFile != "" {
		configFile, err := config.ReplaceAndLoad(configFile, !encrypt)
		if err != nil {
			log.Fatalf("ERROR: failed to load config file %s - %s\n", configFile, err)
		}
	} else {
		log.Fatalln("ERROR: must set config file")
	}

	// 这里用来生成加密或解密配置文件，平时用不到
	if outputConfig != StringEmpty {
		if err := config.OutputConfigToFile(outputConfig, encrypt); err != nil {
			panic(err)
		}
		return
	}

	//取日志配置，如果没有，使用配置文件中的默认配置
	logFile := flagSet.Lookup("logfile").Value.String()
	if logFile != StringEmpty {
		logger.SetLoggerFile(logFile)
	}

	////////////////////SERVICE INIT////////////////////

	//崩溃恢复打印
	defer func() {
		if err := recover(); err != nil {
			log.Printf("ERROR: abort, unknown error, reason:%v,\n stack:%s\n", err, IdentifyPanic())
			fmt.Printf("ERROR: abort, unknown error, reason:%v", err)
		}
	}()

	// 初始化日志组件&ctx
	loggerConfig := logger.GetHlogConfig()

	// 初始化根logger
	l := hlog.NewLoggerWithConfig(loggerConfig, 0)

	// 初始化根ctx用于service
	ctx, cancel := context.WithCancel(context.Background())
	ctx = context.WithValue(ctx, KeyLogger, l)

	// 初始化IDGen
	defaultRedisClient, err := redis.DefaultClient(ctx)
	if err != nil {
		log.Fatalf("ERROR: failed to get default redis client, reason:%v\n", err)
	}
	idgen.Init(defaultRedisClient, 10)

	// 脱敏
	masker.SetMask(true)

	// Pprof
	if http_server.GetConfig().Debug {
		StartPProf(http_server.GetConfig().PProfAddr)
		log.Println("start pprof, addr:", http_server.GetConfig().PProfAddr)
	}

	wg := &WaitGroupWrapper{}

	// 初始化server实例
	engine := http_server.CustomServer(l, true)

	// prometheus实例
	promEngine := iris.New()
	promEngine.Get("/metrics", iris.FromStd(promhttp.Handler()))

	// 注册中间件
	engine.UseRouter(cas.ValidateSSO())
	engine.UseRouter(middleware.ValidateIAMSimple(common.RoleToPathMap))
	engine.UseRouter(middleware.ValidateWebAuthn())

	// 注册接口
	apiParty := engine.Party("/api")
	WrapUserApi(apiParty)
	WrapAssetsApi(apiParty)
	WrapRulesApi(apiParty)
	WrapTransactionApi(apiParty)

	////////////////////SERVICE START////////////////////
	log.Println("service start")

	// 开启比对服务
	srv := engineHandler.NewEngineService()
	srv.StartEngine(ctx, wg)
	srv.StartSync(ctx, wg)
	srv.StartValidate(ctx, wg)

	// 启动web服务
	wg.Wrap(func() {
		err := engine.Listen(http_server.GetConfig().ServeAddr, iris.WithoutInterruptHandler)
		if err != nil {
			log.Println("service start error:", err)
		}
	})

	// 启动prometheus
	wg.Wrap(func() {
		log.Println("start prometheus, addr:", prometheus.GetPrometheusConfig().Addr)
		err := promEngine.Listen(prometheus.GetPrometheusConfig().Addr, iris.WithoutInterruptHandler)
		if err != nil {
			log.Println("prometheus start error:", err)
		}
	})

	////////////////////EXIT CONTROL////////////////////

	// 监听QuitChan，控制idGen注销，日志实例退出
	wg.Wrap(func() {
		<-QuitChan
		cancel()
		idgen.UnRegister()
		log.Println("idgen unregistered")
		l.Close()
		log.Println("logger closed")
	})

	// 优雅关闭server，并关闭QuitChan
	http_server.GracefulExit(engine, promEngine)
	log.Println("service stopping...")

	// 等待server,日志实例全部退出
	wg.Wait()

	// 退出pprof
	if http_server.GetConfig().Debug {
		StopPProf()
	}

	// 服务关闭
	log.Println("service stopped")
}

func WrapAssetsApi(app iris.Party) {
	app.Post("/assets/chain/list", assets.QueryChainList)
	app.Post("/assets/chain/create", assets.CreateChains)
	app.Post("/assets/chain/modify", assets.ModifyChains)
	app.Post("/assets/chain/delete", assets.DeleteChains)
	app.Post("/assets/chain/sync", assets.SyncChains)
	app.Post("/assets/token/list", assets.QueryTokenList)
	app.Post("/assets/token/create", assets.CreateTokens)
	app.Post("/assets/token/modify", assets.ModifyTokens)
	app.Post("/assets/token/delete", assets.DeleteTokens)
	app.Post("/assets/token/sync", assets.SyncTokens)
	app.Post("/assets/platform/list", assets.QueryPlatformList)
	app.Post("/assets/platform/create", assets.CreatePlatforms)
	app.Post("/assets/platform/modify", assets.ModifyPlatforms)
	app.Post("/assets/platform/delete", assets.DeletePlatforms)
	app.Get("/assets/platform/sync", assets.SyncPlatforms)
}

func WrapRulesApi(app iris.Party) {
	app.Post("/rule/list", rule.QueryRuleList)
	app.Get("/rule/detail", rule.QueryRuleDetail)
	app.Post("/rule/create", rule.CreateRule)
	app.Post("/rule/modify", rule.ModifyRules)
	app.Post("/rule/delete", rule.DeleteRules)
	app.Get("/rule/supportParams", rule.SupportParams)
}

func WrapTransactionApi(app iris.Party) {
	app.Post("/transaction/list", transaction.QueryTransactionList)
	app.Post("/transaction/setState", transaction.SetTransactionValidateState)
	app.Post("/order/list", transaction.QueryOrderList)
	app.Post("/order/setState", transaction.SetOrderValidateState)
	app.Post("/order/sync", transaction.SyncOrders)
	app.Post("/statement/list", transaction.QueryStatementList)
	app.Post("/statement/setState", transaction.SetStatementValidateState)
	app.Post("/statement/sync", transaction.SyncStatements)
}

func WrapUserApi(app iris.Party) {
	app.Get("/user/info", user.GetUserInfo)
	app.Get("/user/callback", user.Callback)
	app.Get("/user/logout", user.Logout)
	app.Get("/user/register/begin", user.WebAuthnSignupBegin)
	app.Post("/user/register/finish", user.WebAuthnSignupFinish)
	app.Get("/user/validate/begin", user.WebAuthnValidateBegin)
}
