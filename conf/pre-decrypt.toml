[http_server]
  Environment = "pre"
  ServeAddr = ":10010"
  InterAddr = ":10000"
  PProfAddr = ":10099"
  Debug = true
  AllowOrigins = ["*"]

[Mysql]
  [Mysql.fuse]
    [Mysql.fuse.RW]
      DataSourceName = "fuse_user:1gZot0o0BLkT5FaxPZJN@tcp(sec-db-mysql-test.cjakwymq67dd.ap-northeast-1.rds.amazonaws.com:3306)/fuse_pre?charset=utf8mb4&timeout=200ms&loc=Asia%2FShanghai&parseTime=True"
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1
    [Mysql.fuse.R]
      DataSourceName = "fuse_user:1gZot0o0BLkT5FaxPZJN@tcp(sec-db-mysql-test.cjakwymq67dd.ap-northeast-1.rds.amazonaws.com:3306)/fuse_pre?charset=utf8mb4&timeout=200ms&loc=Asia%2FShanghai&parseTime=True"
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["**********:6379"]
    MaxRetries = -1

[Log]
  LogLevel = "debug"
  LogFile = "/root/logs/fuse.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "fuse-log"
    InjectHostname = true
    App = "fuse"
    AppName = "fuse-pre"
    EnvName = "pre"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true

[Cas]
  EndPoint = "https://sso.sec-test.yorkapp.com"
  FrontDomain = "https://sso.sec-test.yorkapp.com"
  ClientID = "5e93bdf87f668b84ab2c"
  ClientSecret = "1b88f7aec1b2d26ec0b8c2a04563ae89764b7174"
  Cert = "-----BEGIN CERTIFICATE-----\nMIIE2zCCAsOgAwIBAgIDAeJAMA0GCSqGSIb3DQEBCwUAMCcxDzANBgNVBAoTBkF2\nZW5pcjEUMBIGA1UEAwwLY2VydF9yb3N5MmIwHhcNMjUwNDIyMTEwODA1WhcNNDUw\nNDIyMTEwODA1WjAnMQ8wDQYDVQQKEwZBdmVuaXIxFDASBgNVBAMMC2NlcnRfcm9z\neTJiMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAwApy2l97sD+yCENp\nd7gadlFN79tOKBn0aEmNBESZN5+cjZc9FoZjAW5Rn3Nm3hFVcsbqt6u0H8NCC0ZE\nqykbD0uhwySwmqydhTHqU9h1hsckzfWgCHDQ8Kw7Ru8ppu7xAvMNrLFMmmUJdI0+\nHkP9/Arit8MaLLacUk39MyzFdg7DKUFgMNqunJ+f2KV5t/D6jr3ooS0a5AW/t88E\nzkZOOrmw0VKzSEASzAprHiksOJUvqu8BqoYpbQzAx0PABKQviNp3eItOWf+eai/V\nWtOwcQvFbt0Ja/i8fVcheHtmvGNVbIt0ytIJqrJ+BRydJUiD9T7RqMtfU/a3odNq\nBgczamMdZ8n8owyJecADNkUeeCXhGtmCysT2BRcyrB8SBmQy65zD9ydKkgAI4VTa\nrOieAWvxJoauASb/5W+CnWu82d08Mc4vFjceRU/qSSpySKmP1k1lRd7VDOTD0sKc\nvuhy/KPwqxLoR0D9ntuSveGnKt/6O5wPedJ1AAwnZWHVlN+rPP0lre82hjCzlAvp\nUZeFL2uDjjjw05Ek4mKY6B3OSKojxaihRq6o+NUnmjd2iX3zGJaYQupG7diOBQ3o\n/fJ1nSNAoCOnNFXI4UVedczoCgnwLhq4aZLnSct5aSGQ8CZH3YAoWx79hGLvfMpv\nTU4MaTAtneQg7EFL/OgEohrH72UCAwEAAaMQMA4wDAYDVR0TAQH/BAIwADANBgkq\nhkiG9w0BAQsFAAOCAgEAOKrURo1gFmZEWUciwkQhR58SDhUVEEYmX36DOIfReFKX\n/JZM794TkHZ6Vtbl+tM+I5WKoTjMyDLI+I7J1wJAjuKe2yOZxjtr7OTPq/4Smget\nKxATohZeMty1JQyNlIkoAydXIyzGaCKJEIGdNq6RmQ0PAtr0A7mLX07v41qjMHj2\nHxOXZlvbvLzAHFeiIIrCgq/u1L6C1j967eKWqgc1FnZ2aRas4ND/EV0shzq5jT2F\nooeJ2Yocy52RV6HawmJDygYQ7PDbDEYhk+q/+siw2lNZyI8uruQcTQEG1KPmqsaZ\nwA6AVlG3o73maLCjTLsvZhQNgAtM31XNbwm1xzembz7tOKq1tV72QkmkJAMcjhRR\nruHboJlvnMxVxzM3es4blLsZVryqt0uPTO4CJtaSTfGOGUwfrO3J4w/SRY3jd7Zf\n6o4Y8CUsQVSnj4xwLppRstDqk55lOMeEyh4s1h1OJatKRot5EaIk/sIDyhql+2ld\nZ3vtZ/gr8GtmnKhTzRHixgYk25bQQTgPEjBOEeQfYqAYZE+pqBVNpWaN0tZoS8sk\nBPATp++I6l+7ddF0LXRLo6vOdgfo5jT9WsgwUOIAN9X6Q0ddLYAgz1Ra1XVFOFB6\n2y7CFbWnwSUOkSYNGX/tMPAmST7vFzY9VbQhz9Uy9GkPafsOg4buHTme1rlphJo=\n-----END CERTIFICATE-----\n"
  OrgName = "Avenir"
  AppName = "Fuse-test"
  AppDomain = "https://fuse-pre.sec-test.yorkapp.com"
  AppBackDomain = "https://fuse-pre.sec-test.yorkapp.com"
  EnforcerID = ""
  LogoutPath = "/api/user/logout"
  CallbackPath = "/api/user/callback"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "fuse_session_%s"
  SessionTTL = 86400

[WebAuthn]
  RPDisplayName = "fuse-pre"
  RPID = "fuse-pre.sec-test.yorkapp.com"
  RPOrigins = ["https://fuse-pre.sec-test.yorkapp.com"]

[Wallet]
  Host = "https://custody-api-pre.wallet-test.yorkapp.com/"
  ApiKey = "4913ab882b67eb6fe20e44c0a580d8820f0b1cf917fefae771648a2fc95c4435"
  PrivateKey = "ffd5009a1a5c31f369f956156bc3706fb0c72967a378bd91021c0eb687f4870c"

[Notify]
  [Notify.alarm]
    TeamsWebhookUrl = "https://prod-13.southeastasia.logic.azure.com:443/workflows/77852765f7cf4a37bc70df4f09ec437e/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=N7GMiC0ZGcit3nay-TpWy_b_USbuHvwqqCRcxIjzB9k"
    FeishuWebhookUrl = "https://open.larksuite.com/open-apis/bot/v2/hook/b5235220-bdd3-4baf-a313-4ed239e4a91c"
    SmsNumbers = ["+8618519930516"]
    Interval = 180
