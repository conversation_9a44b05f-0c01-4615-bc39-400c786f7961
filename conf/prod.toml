[http_server]
  Environment = "prod"
  ServeAddr = ":10010"
  InterAddr = ":10000"
  PProfAddr = ":10099"
  Debug = true
  AllowOrigins = ["*"]

[Mysql]
  [Mysql.fuse]
    [Mysql.fuse.RW]
      DataSourceName = "aM0ECKoLx2qtSltBTpNBspmwqJVhol4DpJ/Yx1ZksAwLZoi16awN1vnK65UENQBD9USLZ9h2lA3dMFUQ4mNBtS6HKkx3y6A7oKa3fe9iBeBLZJw8R0S3eOCRRVA0qS6Drk4OxtpEf5NtCAfQ4FK54t/k54EtUEnQPQbUc6LpNAFd85Iqq49EELEHqWe8rcvO/T9TuARlexEQFOBMj5r7wt3spKwld8yfFh1zEl/Ld9A="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1
    [Mysql.fuse.R]
      DataSourceName = "aM0ECKoLx2qtSltBTpNBspmwqJVhol4DpJ/Yx1ZksAwLZoi16awN1vnK65UENQBD9USLZ9h2lA3dMFUQ4mNBtS6HKkx3y6A7oKa3fe9iBeBLZJw8R0S3eOCRRVA0qS6Drk4OxtpEf5NtCAfQ4FK54t/k54EtUEnQPQbUc6LpNAFd85Iqq49EELEHqWe8rcvO/T9TuARlexEQFOBMj5r7wt3spKwld8yfFh1zEl/Ld9A="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["clustercfg.sec-vault-prod-fuse-redis.iks5cl.memorydb.ap-northeast-1.amazonaws.com:6379"]
    TLS = true
    Cluster = true

[Log]
  LogLevel = "debug"
  LogFile = "/root/logs/fuse.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "fuse-log"
    InjectHostname = true
    App = "fuse"
    AppName = "fuse-prod"
    EnvName = "prod"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true

[Cas]
  EndPoint = "http://***********:8000"
  FrontDomain = "https://sec-sso.yorkapp.com"
  ClientID = "AkgCH+ao7M5OATCPmFn5bsliEARM8B2/LVxHe0PZDdk="
  ClientSecret = "OCAMNKg9gd2Dsu+Dl7AUYf06xI+qSfXuDshUeO+mGtLh5KcSTwF1g8lLDQrNODOw"
  Cert = "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"
  OrgName = "Avenir"
  AppName = "Fuse"
  AppDomain = "https://fuse.yorkapp.com"
  LogoutPath = "/api/user/logout"
  CallbackPath = "/api/user/callback"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "fuse_session_%s"
  SessionTTL = 86400

[WebAuthn]
  RPDisplayName = "fuse"
  RPID = "fuse.yorkapp.com"
  RPOrigins = ["https://fuse.yorkapp.com"]

[Wallet]
  Host = "https://custody-api.wallet.yorkapp.com/"
  ApiKey = "hrlWWAPtxwJ7QX/ADTSlElQJhfRkzoRcz4+ITDMfuK1Ch4TvhPdi8mhP2MTJHLhzp3QDhGDK9niTX2g41ONAgdrwuBRlkwsdFwceeBD+Omg="
  PrivateKey = "lIeJ3IkG84iwq/2+fTP2/tcxcfvJe0Hw1Eu1LLRuWWIbjIyqSRv2a7jS63qSr42+OX57sv7lxcyEwiZmpu7lAfpu3qbSSM/fdgS7UgZkJlk="

[Notify]
  [Notify.alarm]
    TeamsWebhookUrl = "https://prod-13.southeastasia.logic.azure.com:443/workflows/77852765f7cf4a37bc70df4f09ec437e/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=N7GMiC0ZGcit3nay-TpWy_b_USbuHvwqqCRcxIjzB9k"
    FeishuWebhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/12bf673b-2599-4faa-8861-7d775b7f67ac"
    Interval = 180
    SmsNumbers = ["+8618519930516"]
