[http_server]
  Environment = "test"
  ServeAddr = ":10010"
  InterAddr = ":10000"
  PProfAddr = ":10099"
  Debug = true
  AllowOrigins = ["*"]

[Mysql]
  [Mysql.fuse]
    [Mysql.fuse.RW]
      DataSourceName = "fAIcsIxf9KKUAX2J5yTP2iibFgzQU99OoA2V1hFk8LpVLK/UnjEn3yS68bCEIyMxardcOpX7zXH6xW4ERPdHkqbBcoAqHK3K8t35aYf7OtoKM4zL53NENT77H8H6q9f3eVRyff6G6WCMPXO4dRXC+jaFwWGj8OW/526oZGP4u6n1scu3EVJyTLfbutNxmr7LkJFmd5flqnHM+8tZ/7SQfRebrNBRSQVajBnmkD8pa6Q="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1
    [Mysql.fuse.R]
      DataSourceName = "fAIcsIxf9KKUAX2J5yTP2iibFgzQU99OoA2V1hFk8LpVLK/UnjEn3yS68bCEIyMxardcOpX7zXH6xW4ERPdHkqbBcoAqHK3K8t35aYf7OtoKM4zL53NENT77H8H6q9f3eVRyff6G6WCMPXO4dRXC+jaFwWGj8OW/526oZGP4u6n1scu3EVJyTLfbutNxmr7LkJFmd5flqnHM+8tZ/7SQfRebrNBRSQVajBnmkD8pa6Q="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["**********:6379"]
    MaxRetries = -1

[Log]
  LogLevel = "debug"
  LogFile = "/root/logs/fuse.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "fuse-log"
    InjectHostname = true
    App = "fuse"
    AppName = "fuse-dev"
    EnvName = "dev"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true

[Cas]
  EndPoint = "https://sso.sec-test.yorkapp.com"
  FrontDomain = "https://sso.sec-test.yorkapp.com"
  ClientID = "D9L9oC2UmzStPvKcUoD9twwtqe+xOOvhq/MZieQV5Ws="
  ClientSecret = "0GOEpG0n//dfpBfDzzaTBclszLw8YmV7eJfI4rAHxPT0teilf7LDLNeZdhZ2zLCt"
  Cert = "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"
  OrgName = "Avenir"
  AppName = "Fuse-test"
  AppDomain = "https://fuse.sec-test.yorkapp.com"
  LogoutPath = "/api/user/logout"
  CallbackPath = "/api/user/callback"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "fuse_session_%s"
  SessionTTL = 86400

[WebAuthn]
  RPDisplayName = "fuse"
  RPID = "fuse.sec-test.yorkapp.com"
  RPOrigins = ["https://fuse.sec-test.yorkapp.com"]

[Wallet]
  Host = "https://custody-api.wallet-test.yorkapp.com/"
  ApiKey = "yUpBSCYvzikDv2EbR0Sp7hiyosG7sOfyM6on/zpg8QbNzAwQo/JPfCBd197vpEnNQyG0BCy3yYb9qEF65ZCN1/paUqr4xKIj6+6bICXT1Pc="
  PrivateKey = "XI3v4xCWBtO7buszx92Ht99l3pI+2CtIxJQL74tix6gnCAszvo/elTQCY8c/XsP7CLd7+VB8LGGyW5BE/isf//gIp+9h7YZ1GOQXf8t0JSs="

[Notify]
  [Notify.alarm]
    TeamsWebhookUrl = "https://prod-13.southeastasia.logic.azure.com:443/workflows/77852765f7cf4a37bc70df4f09ec437e/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=N7GMiC0ZGcit3nay-TpWy_b_USbuHvwqqCRcxIjzB9k"
    FeishuWebhookUrl = "https://open.larksuite.com/open-apis/bot/v2/hook/c64f37cb-a154-48b6-aa12-679290617e19"
    SmsNumbers = ["+8618519930516"]
    Interval = 180