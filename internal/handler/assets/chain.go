/**
 * @note
 * chain
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"
)

func QueryChainList(ctx iris.Context) {
	req := &QueryChainListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &QueryChainListResponse{}
	ret.Items, ret.Total, err = assetsLogic.QueryChainList(ctx, req.Page, req.PerPage,
		req.ChainIDs, req.FuseType, req.FuseStatus)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryChainList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateChains(ctx iris.Context) {
	req := &CreateChainsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	for _, chain := range req.Chains {
		if err := chain.ParseRuleIDs(); err != nil {
			SetRet(ctx, NewError(ErrCodeParam, err))
			return
		}
	}
	err := assetsLogic.CreateChains(ctx, req.Chains)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrCreateChain, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationChainCreate, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func ModifyChains(ctx iris.Context) {
	req := &ModifyChainsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	for _, chain := range req.Chains {
		if err := chain.ParseRuleIDs(); err != nil {
			SetRet(ctx, NewError(ErrCodeParam, err))
			return
		}
	}
	err := assetsLogic.ModifyChains(ctx, req.Chains)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrModifyChain, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationChainModify, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteChains(ctx iris.Context) {
	req := &DeleteChainsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.DeleteChains(ctx, req.ChainIDs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrDeleteChain, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationChainDelete, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func SyncChains(ctx iris.Context) {
	count, err := assetsLogic.SyncChains(ctx)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSyncChain, err))
		return
	}
	ret := &SyncChainsResponse{
		Count: count,
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}
