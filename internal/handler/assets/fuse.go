/**
 * @note
 * fuse
 *
 * <AUTHOR>
 * @date 	2025-05-07
 */
package assets

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func QueryAssetsFuseState(ctx iris.Context) {
	req := &QueryAssetsFuseStateRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	fused, err := assetsLogic.QueryAssetsFuseState(ctx, req.ChainID, req.TokenID)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryAssetsFuseState, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(&QueryAssetsFuseStateResponse{Fused: fused}))
}

func QueryAllFuseState(ctx iris.Context) {
	response, err := assetsLogic.QueryAllFuseState(ctx)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryAllFuseState, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(response))
}
