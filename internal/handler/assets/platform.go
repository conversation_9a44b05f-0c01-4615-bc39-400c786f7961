/**
 * @note
 * platform
 *
 * <AUTHOR>
 * @date 	2025-05-08
 */
package assets

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"
)

func QueryPlatformList(ctx iris.Context) {
	req := &QueryPlatformListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &QueryPlatformListResponse{}
	ret.Items, ret.Total, err = assetsLogic.QueryPlatformList(ctx, req.Page, req.PerPage,
		req.<PERSON>etIDs, req.SyncStatus)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryPlatformList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreatePlatforms(ctx iris.Context) {
	req := &CreatePlatformsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.CreatePlatforms(ctx, req.Platforms)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrCreatePlatform, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationPlatformCreate, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func ModifyPlatforms(ctx iris.Context) {
	req := &ModifyPlatformsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.ModifyPlatforms(ctx, req.Platforms)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrModifyPlatform, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationPlatformModify, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeletePlatforms(ctx iris.Context) {
	req := &DeletePlatformsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.DeletePlatforms(ctx, req.WalletIDs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrDeletePlatform, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationPlatformDelete, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func SyncPlatforms(ctx iris.Context) {
	_, err := assetsLogic.SyncPlatforms(ctx)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSyncPlatform, err))
		return
	}

	SetRet(ctx, NewError(ErrCodeOK))
}
