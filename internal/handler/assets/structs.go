/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type QueryChainListRequest struct {
	Page       int                    `json:"page" validate:"gte=0"`
	PerPage    int                    `json:"perPage" validate:"gt=0"`
	ChainIDs   []string               `json:"chainIDs"`
	FuseType   *fuseCommon.FuseType   `json:"fuseType" validate:"omitempty,oneof=0 1"`
	FuseStatus *fuseCommon.FuseStatus `json:"fuseStatus" validate:"omitempty,oneof=0 1"`
}

type QueryChainListResponse struct {
	Total int64                    `json:"total"`
	Items []*assetsLogic.ChainItem `json:"items"`
}

type QueryTokenListRequest struct {
	Page       int                    `json:"page" validate:"gte=0"`
	PerPage    int                    `json:"perPage" validate:"gt=0"`
	TokenIDs   []string               `json:"tokenIDs"`
	ChainIDs   []string               `json:"chainIDs"`
	FuseType   *fuseCommon.FuseType   `json:"fuseType" validate:"omitempty,oneof=0 1"`
	FuseStatus *fuseCommon.FuseStatus `json:"fuseStatus" validate:"omitempty,oneof=0 1"`
}

type QueryTokenListResponse struct {
	Total int64                    `json:"total"`
	Items []*assetsLogic.TokenItem `json:"items"`
}

type ModifyChainsRequest struct {
	Chains []*assetsLogic.ModifyChainInput `json:"chains"`
}

type CreateChainsRequest struct {
	Chains []*assetsLogic.CreateChainInput `json:"chains" validate:"required"`
}

type DeleteChainsRequest struct {
	ChainIDs []string `json:"chainIDs"`
}

type ModifyTokensRequest struct {
	Tokens []*assetsLogic.ModifyTokenInput `json:"tokens" validate:"required"`
}

type CreateTokensRequest struct {
	Tokens []*assetsLogic.CreateTokenInput `json:"tokens" validate:"required"`
}

type DeleteTokensRequest struct {
	TokenIDs []string `json:"tokenIDs"`
}

type SyncTokensRequest struct {
	ChainID string `json:"chainID" validate:"required"`
}

type QueryAssetsFuseStateRequest struct {
	ChainID string `json:"chain_id" validate:"required"`
	TokenID string `json:"token_id"`
}

type QueryAssetsFuseStateResponse struct {
	Fused bool `json:"fused"`
}

type QueryAllFuseStateItem struct {
	ChainID string `json:"chain_id,omitempty"`
	TokenID string `json:"token_id,omitempty"`
}

type QueryAllFuseStateResponse struct {
	Items []*QueryAllFuseStateItem `json:"items"`
}

type QueryPlatformListRequest struct {
	Page       int                    `json:"page" validate:"gte=0"`
	PerPage    int                    `json:"perPage" validate:"gt=0"`
	WalletIDs  []string               `json:"walletIDs"`
	SyncStatus *fuseCommon.SyncStatus `json:"syncStatus" validate:"omitempty,oneof=0 1"`
}

type QueryPlatformListResponse struct {
	Total int64                       `json:"total"`
	Items []*assetsLogic.PlatformItem `json:"items"`
}

type ModifyPlatformsRequest struct {
	Platforms []*assetsLogic.ModifyPlatformInput `json:"platforms" validate:"required"`
}

type CreatePlatformsRequest struct {
	Platforms []*assetsLogic.CreatePlatformInput `json:"platforms" validate:"required"`
}

type DeletePlatformsRequest struct {
	WalletIDs []string `json:"walletIDs" validate:"required"`
}

type SyncChainsResponse struct {
	Count int `json:"count"`
}
