/**
 * @note
 * token
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"
)

func QueryTokenList(ctx iris.Context) {
	req := &QueryTokenListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &QueryTokenListResponse{}
	ret.Items, ret.Total, err = assetsLogic.QueryTokenList(ctx, req.Page, req.PerPage,
		req.TokenIDs, req.<PERSON>s, req.FuseType, req.FuseStatus)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryTokenList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func CreateTokens(ctx iris.Context) {
	req := &CreateTokensRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	for _, chain := range req.Tokens {
		if err := chain.ParseRuleIDs(); err != nil {
			SetRet(ctx, NewError(ErrCodeParam, err))
			return
		}
	}
	err := assetsLogic.CreateTokens(ctx, req.Tokens)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrCreateToken, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationTokenCreate, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func ModifyTokens(ctx iris.Context) {
	req := &ModifyTokensRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	for _, chain := range req.Tokens {
		if err := chain.ParseRuleIDs(); err != nil {
			SetRet(ctx, NewError(ErrCodeParam, err))
			return
		}
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.ModifyTokens(ctx, req.Tokens)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrModifyToken, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationTokenModify, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteTokens(ctx iris.Context) {
	req := &DeleteTokensRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.DeleteTokens(ctx, req.TokenIDs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrDeleteToken, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationTokenDelete, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func SyncTokens(ctx iris.Context) {
	req := &SyncTokensRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	err := assetsLogic.SyncTokens(ctx, req.ChainID)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSyncToken, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK))
}
