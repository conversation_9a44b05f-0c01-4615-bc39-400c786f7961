/**
 * @note
 * engine
 *
 * <AUTHOR>
 * @date 	2025-04-25
 */
package engine

import (
	"context"
	"time"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/fuse/internal/logic/executor"
	"gitlab.docsl.com/security/fuse/internal/logic/rule_engine"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (s *EngineService) StartEngine(ctx context.Context, wg *WaitGroupWrapper) {
	ruleMaxUpdateTime, ruleRelationMaxUpdateTime, tokenMaxUpdateTime, chainMaxUpdateTime,
		ruleRelationCount, err := executor.GetMaxUpdateTimesAndCount(ctx)
	if err != nil {
		panic("fuse engine service starting failed, get max update time error:" + err.Error())
	}
	if err = rule_engine.InitEngine(ctx); err != nil {
		panic("fuse engine service init rule engine error:" + err.<PERSON>rror())
	}
	GetLogger(ctx).Info("fuse engine service start")
	wg.Wrap(func() {
		ticker := time.NewTicker(fuseCommon.RefreshInterval * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-QuitChan:
				GetLogger(ctx).Infoln("fuse engine service get QuitChan, exit")
				return
			case <-ticker.C:
				select {
				case <-QuitChan:
					GetLogger(ctx).Infoln("fuse engine service get QuitChan, exit")
					return
				default:
					newRuleMaxUpdateTime, newRuleRelationMaxUpdateTime, newTokenMaxUpdateTime, newChainMaxUpdateTime,
						newRuleRelationCount, err := executor.GetMaxUpdateTimesAndCount(ctx)
					if err != nil {
						GetLogger(ctx).Errorf("fuse engine service get max update time error: %v", err)
						continue
					}
					if newRuleMaxUpdateTime != ruleMaxUpdateTime || newRuleRelationMaxUpdateTime != ruleRelationMaxUpdateTime ||
						newTokenMaxUpdateTime != tokenMaxUpdateTime || newChainMaxUpdateTime != chainMaxUpdateTime ||
						newRuleRelationCount != ruleRelationCount { // 有些东西变了，直接全部重新编译rule engine
						GetLogger(ctx).Infoln("fuse engine service has detected some configuration changes. Reinitializing the engine.")
						// 更新引擎
						if err = rule_engine.InitEngine(ctx); err != nil {
							GetLogger(ctx).Errorf("fuse engine service init rule engine error: %v", err)
							continue
						}
						// 更新时间记录
						ruleMaxUpdateTime, ruleRelationMaxUpdateTime, tokenMaxUpdateTime, chainMaxUpdateTime, ruleRelationCount =
							newRuleMaxUpdateTime, newRuleRelationMaxUpdateTime, newTokenMaxUpdateTime, newChainMaxUpdateTime, newRuleRelationCount
					}
				}
			}
		}
	})
}
