/**
 * @note
 * syncer
 *
 * <AUTHOR>
 * @date 	2025-04-25
 */
package engine

import (
	"context"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	"golang.org/x/sync/errgroup"
	"time"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/masker"
	"gitlab.docsl.com/security/common/redis"
	syncerLogic "gitlab.docsl.com/security/fuse/internal/logic/syncer"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	transactionModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

const (
	initTimestamp          = 1735689600000
	timeOffsetMilliSeconds = -10000
	timeOffset             = timeOffsetMilliSeconds * time.Millisecond
)

func (s *EngineService) StartSync(ctx context.Context, wg *WaitGroupWrapper) {
	redisClient, err := redis.DefaultClient(ctx)
	if err != nil {
		panic("fuse engine service redis client error:" + err.Error())
	}
	// 记录statement的最新进度
	walletIDChainIDMaxTimestampMap, err := transactionModel.QueryStatementMaxTimestampGroupByWalletIDAndChainID(ctx)
	if err != nil {
		panic("fuse syncer service QueryStatementMaxTimestampGroupByWalletIDAndChainID error:" + err.Error())
	}
	// 记录platform信息
	walletIDPlatformMap := make(map[string]*assetsModel.PlatformTable)
	err = getWalletIDPlatformMapAndDecrypt(ctx, walletIDPlatformMap)
	if err != nil {
		panic("fuse syncer service getWalletIDPlatformMapAndDecrypt error:" + err.Error())
	}
	// 记录order的进度
	depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap := make(map[string]*time.Time), make(map[string]*time.Time)
	if err = getOrderHostUpdateTimeMap(ctx, walletIDPlatformMap, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap); err != nil {
		panic("fuse syncer service GetOrderHostUpdateTimeMap error:" + err.Error())
	}
	GetLogger(ctx).Info("fuse syncer service start")
	wg.Wrap(func() {
		leader, leaderWg := redisClient.ElectLeader(fuseCommon.SyncerLeaderKey,
			time.Duration(fuseCommon.LeaderExpire)*time.Second,
			time.Duration(fuseCommon.LeaderInterval)*time.Second,
			fuseCommon.Hostname)
		ticker := time.NewTicker(fuseCommon.RefreshInterval * time.Second)
		defer ticker.Stop()
		chainSyncTicker := time.NewTicker(fuseCommon.ChainSyncerRefreshInterval * time.Second)
		defer chainSyncTicker.Stop()
		for {
			select {
			case <-QuitChan:
				GetLogger(ctx).Infoln("fuse syncer service get QuitChan, exit")
				leaderWg.Wait()
				return
			case <-chainSyncTicker.C:
				select {
				case <-QuitChan:
					GetLogger(ctx).Infoln("fuse syncer service get QuitChan, exit")
					leaderWg.Wait()
					return
				default:
					if _, err = assetsLogic.SyncChains(ctx); err != nil {
						GetLogger(ctx).Errorf("fuse syncer service sync all chains from wallet error: %v", err)
					}
					if err = assetsLogic.SyncTokens(ctx, "all"); err != nil {
						GetLogger(ctx).Errorf("fuse syncer service sync all tokens from wallet error: %v", err)
					}
				}
			case <-ticker.C:
				select {
				case <-QuitChan:
					GetLogger(ctx).Infoln("fuse syncer service get QuitChan, exit")
					leaderWg.Wait()
					return
				default:
					if !leader.IsLeader() {
						GetLogger(ctx).Infof("fuse syncer service not leader, leader is [%s], sleep...", leader.LeaderHost())
						continue
					}
					// 拉取所有需要同步的chain列表
					chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{})
					if err != nil {
						GetLogger(ctx).Errorf("fuse syncer service get all chains error: %v", err)
						continue
					}
					chainIDs := make([]string, 0)
					for _, chain := range chains {
						chainIDs = append(chainIDs, chain.ChainID)
					}
					// 记录platform信息
					err = getWalletIDPlatformMapAndDecrypt(ctx, walletIDPlatformMap)
					if err != nil {
						GetLogger(ctx).Errorf("fuse syncer service get all platforms error: %v", err)
						continue
					}
					// 拉取需要同步的wallet_id列表，顺便同步order的进度
					if err = getOrderHostUpdateTimeMap(ctx, walletIDPlatformMap, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap); err != nil {
						GetLogger(ctx).Errorf("fuse syncer service GetOrderHostUpdateTimeMap error: %v", err)
						continue
					}
					printLog(ctx, "fuse syncer service walletIDChainIDMaxTimestampMap: ", walletIDChainIDMaxTimestampMap)
					printLog(ctx, "fuse syncer service depositHostMaxUpdateTimeMap: ", depositHostMaxUpdateTimeMap)
					printLog(ctx, "fuse syncer service withdrawHostMaxUpdateTimeMap: ", withdrawHostMaxUpdateTimeMap)
					// 拉取流水
					err = s.syncStatementOnce(ctx, chainIDs, walletIDChainIDMaxTimestampMap, walletIDPlatformMap)
					if err != nil {
						GetLogger(ctx).Errorf("fuse syncer service sync statement error: %v", err)
					}
					err = s.syncDepositOrderOnce(ctx, chainIDs, depositHostMaxUpdateTimeMap)
					if err != nil {
						GetLogger(ctx).Errorf("fuse syncer service sync deposit order error: %v", err)
					}
					err = s.syncWithdrawOrderOnce(ctx, chainIDs, withdrawHostMaxUpdateTimeMap)
					if err != nil {
						GetLogger(ctx).Errorf("fuse syncer service sync withdraw order error: %v", err)
					}
				}
			}
		}
	})
}

func (s *EngineService) syncStatementOnce(ctx context.Context, chainIDs []string, walletIDChainIDMaxTimestampMap map[string]map[string]int64,
	walletIDPlatformMap map[string]*assetsModel.PlatformTable) error {
	endTime := time.Now().UnixMilli()
	for walletID, platform := range walletIDPlatformMap {
		if walletIDChainIDMaxTimestampMap[walletID] == nil {
			walletIDChainIDMaxTimestampMap[walletID] = make(map[string]int64)
		}
		chainIDMaxTimestampMap := walletIDChainIDMaxTimestampMap[walletID]
		for _, chainID := range chainIDs {
			maxTimestamp := chainIDMaxTimestampMap[chainID]
			if maxTimestamp == 0 {
				maxTimestamp = initTimestamp // 设一个最初始的值
			}
			totalSynced, newMaxTimestamp, err := syncerLogic.SyncStatementOnceByWalletIDAndChainID(ctx, platform.Config.WalletHost,
				platform.Config.WalletApiKey, platform.Config.WalletApiSecret, walletID, chainID, maxTimestamp, endTime)
			if err != nil {
				GetLogger(ctx).Errorf("SyncStatementOnce: SyncStatementOnceByWalletIDAndChainID from %d to %d failed for wallet %s on chain %s: %v",
					maxTimestamp, endTime, walletID, chainID, err)
				return err
			}
			GetLogger(ctx).Infof("SyncStatementOnce: SyncStatementOnceByWalletIDAndChainID from %d to %d success for wallet %s on chain %s, totalSynced: %d, latestTime: %d",
				maxTimestamp, endTime, walletID, chainID, totalSynced, newMaxTimestamp)
			if newMaxTimestamp > maxTimestamp {
				maxTimestamp = newMaxTimestamp + timeOffsetMilliSeconds // 少30秒，覆盖边界情况
			}
			chainIDMaxTimestampMap[chainID] = maxTimestamp
		}
	}
	return nil
}

func (s *EngineService) syncDepositOrderOnce(ctx context.Context, chainIDs []string, depositHostMaxUpdateTimeMap map[string]*time.Time) error {
	endTime := time.Now().UnixMilli()
	eg, ctx := errgroup.WithContext(ctx)
	for host, maxUpdateTime := range depositHostMaxUpdateTimeMap {
		h, t := host, maxUpdateTime
		eg.Go(func() error {
			totalSynced, latestTimeMilli, err := syncerLogic.SyncDepositOrderOnce(ctx, h, chainIDs, t.UnixMilli(), endTime)
			if err != nil {
				GetLogger(ctx).Errorf("SyncDepositOrderOnce host [%s] from %d to %d failed, totalSynced: %d, latestTime: %d, error: %v",
					h, t.UnixMilli(), endTime, totalSynced, latestTimeMilli, err)
				return err
			}
			GetLogger(ctx).Infof("SyncDepositOrderOnce host [%s] from %d to %d success, totalSynced: %d, latestTime: %d",
				h, t.UnixMilli(), endTime, totalSynced, latestTimeMilli)
			if latestTimeMilli > t.UnixMilli() {
				*t = time.UnixMilli(latestTimeMilli + timeOffsetMilliSeconds) // 少10秒，覆盖边界情况
			}
			return err
		})
	}
	return eg.Wait()
}

func (s *EngineService) syncWithdrawOrderOnce(ctx context.Context, chainIDs []string, withdrawHostMaxUpdateTimeMap map[string]*time.Time) error {
	endTime := time.Now().UnixMilli()
	eg, ctx := errgroup.WithContext(ctx)
	for host, maxUpdateTime := range withdrawHostMaxUpdateTimeMap {
		h, t := host, maxUpdateTime
		eg.Go(func() error {
			totalSynced, latestTimeMilli, err := syncerLogic.SyncWithdrawOrderOnce(ctx, h, chainIDs, t.UnixMilli(), endTime)
			if err != nil {
				GetLogger(ctx).Errorf("SyncWithdrawOrderOnce host [%s] from %d to %d failed, totalSynced: %d, latestTime: %d, error: %v",
					h, t.UnixMilli(), endTime, totalSynced, latestTimeMilli, err)
				return err
			}
			GetLogger(ctx).Infof("SyncWithdrawOrderOnce host [%s] from %d to %d success, totalSynced: %d, latestTime: %d",
				h, t.UnixMilli(), endTime, totalSynced, latestTimeMilli)
			if latestTimeMilli > t.UnixMilli() {
				*t = time.UnixMilli(latestTimeMilli + timeOffsetMilliSeconds) // 少10秒，覆盖边界情况
			}
			return err
		})
	}
	return eg.Wait()
}

func getWalletIDPlatformMapAndDecrypt(ctx context.Context, walletIDPlatformMap map[string]*assetsModel.PlatformTable) error {
	status := fuseCommon.SyncStatusOn
	platforms, err := assetsModel.QueryPlatformBySeveralConditions(ctx, assetsModel.QueryPlatformFilter{SyncStatus: &status})
	if err != nil {
		return err
	}
	for _, platform := range platforms {
		decryptedConfig, err := masker.PropCrypt(platform.Config)
		if err != nil {
			return err
		}
		platform.Config = decryptedConfig.(*assetsModel.PlatformConfig)
		walletIDPlatformMap[platform.WalletID] = platform
	}
	return nil
}

func getOrderHostUpdateTimeMap(ctx context.Context, walletIDPlatformMap map[string]*assetsModel.PlatformTable, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap map[string]*time.Time) (err error) {
	for _, platform := range walletIDPlatformMap {
		if depositHostMaxUpdateTimeMap[platform.Config.OrderHost] == nil { // 有新配置的平台，从库里重新更新
			return initOrderHostUpdateTimeMap(ctx, walletIDPlatformMap, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap)
		}
		if withdrawHostMaxUpdateTimeMap[platform.Config.OrderHost] == nil { // 有新配置的平台，从库里重新更新
			return initOrderHostUpdateTimeMap(ctx, walletIDPlatformMap, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap)
		}
	}
	return
}

func initOrderHostUpdateTimeMap(ctx context.Context, walletIDPlatformMap map[string]*assetsModel.PlatformTable, depositHostMaxUpdateTimeMap, withdrawHostMaxUpdateTimeMap map[string]*time.Time) (err error) {
	// 记录depositOrder的进度
	depositWalletIDMaxUpdateTimeMap, err := transactionModel.QueryDepositOrderMaxUpdateTimeGroupByWalletID(ctx)
	if err != nil {
		return err
	}
	withdrawWalletIDMaxUpdateTimeMap, err := transactionModel.QueryWithdrawOrderMaxUpdateTimeGroupByWalletID(ctx)
	if err != nil {
		return err
	}
	for _, platform := range walletIDPlatformMap {
		if depositHostMaxUpdateTimeMap[platform.Config.OrderHost] == nil {
			t := time.UnixMilli(initTimestamp)
			depositHostMaxUpdateTimeMap[platform.Config.OrderHost] = &t
		}
		if withdrawHostMaxUpdateTimeMap[platform.Config.OrderHost] == nil {
			t := time.UnixMilli(initTimestamp)
			withdrawHostMaxUpdateTimeMap[platform.Config.OrderHost] = &t
		}
		if depositHostMaxUpdateTimeMap[platform.Config.OrderHost].Before(depositWalletIDMaxUpdateTimeMap[platform.WalletID]) {
			t := depositWalletIDMaxUpdateTimeMap[platform.WalletID]
			depositHostMaxUpdateTimeMap[platform.Config.OrderHost] = &t
		}
		if withdrawHostMaxUpdateTimeMap[platform.Config.OrderHost].Before(withdrawWalletIDMaxUpdateTimeMap[platform.WalletID]) {
			t := withdrawWalletIDMaxUpdateTimeMap[platform.WalletID]
			withdrawHostMaxUpdateTimeMap[platform.Config.OrderHost] = &t
		}
	}
	return nil
}

func printLog(ctx context.Context, prefix string, value interface{}) {
	valueStr, _ := JsonStringEncode(value)
	GetLogger(ctx).Infof(prefix + valueStr)
}
