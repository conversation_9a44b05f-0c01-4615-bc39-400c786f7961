/**
 * @note
 * engine
 *
 * <AUTHOR>
 * @date 	2025-04-08
 */
package engine

import (
	"context"
	"time"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/redis"
	"gitlab.docsl.com/security/fuse/internal/logic/executor"
	"gitlab.docsl.com/security/fuse/internal/logic/matcher"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type EngineService struct{}

func NewEngineService() *EngineService {
	return &EngineService{}
}

func (s *EngineService) StartValidate(ctx context.Context, wg *WaitGroupWrapper) {
	redisClient, err := redis.DefaultClient(ctx)
	if err != nil {
		panic("fuse engine service redis client error:" + err.Error())
	}
	GetLogger(ctx).Info("fuse validator service start")
	wg.Wrap(func() {
		leader, leaderWg := redisClient.ElectLeader(fuseCommon.ValidatorLeaderKey,
			time.Duration(fuseCommon.LeaderExpire)*time.Second,
			time.Duration(fuseCommon.LeaderInterval)*time.Second,
			fuseCommon.Hostname)
		ticker := time.NewTicker(fuseCommon.RefreshInterval * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-QuitChan:
				GetLogger(ctx).Infoln("fuse validator service get QuitChan, exit")
				leaderWg.Wait()
				return
			case <-ticker.C:
				select {
				case <-QuitChan:
					GetLogger(ctx).Infoln("fuse validator service get QuitChan, exit")
					leaderWg.Wait()
					return
				default:
					if !leader.IsLeader() {
						GetLogger(ctx).Infof("fuse validator service not leader, leader is [%s], sleep...", leader.LeaderHost())
						continue
					}
					// 使用提现流水进行匹配
					if err := matcher.StatementMatch(ctx, fuseCommon.StatementBusinessTypeWithdraw, nil); err != nil {
						GetLogger(ctx).Errorln("fuse validator service match withdraw statements error:", err)
					}
					// 使用充值回滚流水进行匹配
					stateRevert := fuseCommon.StatementStateRevert
					if err := matcher.StatementMatch(ctx, fuseCommon.StatementBusinessTypeDeposit, &stateRevert); err != nil {
						GetLogger(ctx).Errorln("fuse validator service match deposit revert statements error:", err)
					}
					// 使用订单进行匹配
					if err := matcher.OrderMatch(ctx); err != nil {
						GetLogger(ctx).Errorln("fuse validator service match orders error:", err)
					}
					// 使用unit匹配两边流水+订单
					if err := matcher.TransactionUnitMatch(ctx); err != nil {
						GetLogger(ctx).Errorln("fuse validator service match units error:", err)
					}
					// 执行规则校验
					abnormalUnits, err := executor.ValidateUnits(ctx)
					if err != nil {
						GetLogger(ctx).Errorln("fuse validator service validate units error:", err)
						continue
					}
					for _, abnormalUnit := range abnormalUnits {
						GetLogger(ctx).Infoln("fuse validator service discover abnormalUnitID:", abnormalUnit.ID, "orderIDs:", abnormalUnit.OrderIDs, "statementIDs:", abnormalUnit.StatementIDs)
					}
					// 执行后续动作
					err = executor.ExecuteRuleAction(ctx, abnormalUnits)
					if err != nil {
						GetLogger(ctx).Errorln("fuse validator service execute rule action error:", err)
					}
				}
			}
		}
	})
}
