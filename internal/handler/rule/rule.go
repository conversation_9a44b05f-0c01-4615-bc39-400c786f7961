/**
 * @note
 * rule
 *
 * <AUTHOR>
 * @date 	2025-04-21
 */
package rule

import (
	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	assetsLogic "gitlab.docsl.com/security/fuse/internal/logic/assets"
	ruleLogic "gitlab.docsl.com/security/fuse/internal/logic/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"
)

func QueryRuleList(ctx iris.Context) {
	req := &QueryRuleListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	var err error
	ret := &QueryRuleListResponse{}
	ret.Items, ret.Total, err = ruleLogic.QueryRuleList(ctx, req.Page, req.PerPage,
		req.ChainID, req.TokenID, req.Status)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryRuleList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

func QueryRuleDetail(ctx iris.Context) {
	req := &QueryRuleDetailRequest{}
	errQuery := ctx.ReadQuery(req)
	if errQuery != nil {
		SetRet(ctx, NewError(ErrCodeParam, errQuery))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	item, err := ruleLogic.QueryRuleDetail(ctx, req.RuleID)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryRuleDetail, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(item))
}

func CreateRule(ctx iris.Context) {
	req := &CreateRuleRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	} else if errValidate = req.RuleDetail.Validate(); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	ruleItem, err := ruleLogic.CreateRule(ctx, req.RuleName, req.Desc, req.Priority, req.RuleStatus,
		&req.RuleDetail, &req.Actions)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrCreateRule, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationRuleCreate, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ruleItem))
}

func ModifyRules(ctx iris.Context) {
	req := &ModifyRulesRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	// 将ModifyRuleInput转换为ruleLogic.ModifyRuleInput
	inputs := make([]*ruleLogic.ModifyRuleInput, 0, len(req.Rules))
	for _, rule := range req.Rules {
		if rule.RuleDetail != nil {
			if errValidate := rule.RuleDetail.Validate(); errValidate != nil {
				SetRet(ctx, NewError(ErrCodeParam, errValidate))
				return
			}
		}
		inputs = append(inputs, &ruleLogic.ModifyRuleInput{
			RuleID:     rule.RuleID,
			RuleName:   rule.RuleName,
			Desc:       rule.Desc,
			Priority:   rule.Priority,
			RuleStatus: rule.RuleStatus,
			RuleDetail: rule.RuleDetail,
			Actions:    rule.Actions,
		})
	}

	err := ruleLogic.ModifyRules(ctx, inputs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrModifyRule, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationRuleModify, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func DeleteRules(ctx iris.Context) {
	req := &DeleteRulesRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	ruleIDs, errConvert := StringsToInt64s(req.RuleIDs)
	if errConvert != nil {
		SetRet(ctx, NewError(ErrCodeParam, errConvert))
		return
	}
	err := ruleLogic.DeleteRules(ctx, ruleIDs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrDeleteRule, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationRuleDelete, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func SupportParams(ctx iris.Context) {
	platforms, _, err := assetsLogic.QueryPlatformList(ctx, 0, 0, nil, nil)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryPlatformList, err))
		return
	}
	walletIDMap := make(map[string]interface{})
	for _, platform := range platforms {
		walletIDMap[platform.WalletID] = platform.WalletID
	}
	SetRet(ctx, NewError(ErrCodeOK, []*SupportParam{
		{
			Name:  "平台ID(walletID)",
			Value: fuseCommon.ParamWalletID,
			SupportOps: []*SupportOp{
				{
					Name:             "in",
					Value:            "in",
					SupportValueType: "multiSelect",
					SupportValueEnum: walletIDMap,
				},
				{
					Name:             "notIn",
					Value:            "notIn",
					SupportValueType: "multiSelect",
					SupportValueEnum: walletIDMap,
				},
			},
		}, {
			Name:  "延迟（秒）",
			Value: fuseCommon.ParamLatency,
			SupportOps: []*SupportOp{
				{
					Name:             ">",
					Value:            ">",
					SupportValueType: "int",
				},
				{
					Name:             "<",
					Value:            "<",
					SupportValueType: "int",
				},
			},
		},
		{
			Name:  "数量差",
			Value: fuseCommon.ParamAmountDifference,
			SupportOps: []*SupportOp{
				{
					Name:             ">",
					Value:            ">",
					SupportValueType: "string",
				},
				{
					Name:             "<",
					Value:            "<",
					SupportValueType: "string",
				},
			},
		},
		{
			Name:  "交易类型",
			Value: fuseCommon.ParamTransactionType,
			SupportOps: []*SupportOp{
				{
					Name:             "in",
					Value:            "in",
					SupportValueType: "multiSelect",
					SupportValueEnum: map[string]interface{}{
						"充值": 0,
						"提现": 1,
					},
				},
				{
					Name:             "notIn",
					Value:            "notIn",
					SupportValueType: "multiSelect",
					SupportValueEnum: map[string]interface{}{
						"充值": 0,
						"提现": 1,
					},
				},
			},
		},
	}))
}
