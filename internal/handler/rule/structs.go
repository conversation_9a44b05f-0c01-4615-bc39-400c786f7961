/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-04-21
 */
package rule

import (
	ruleLogic "gitlab.docsl.com/security/fuse/internal/logic/rule"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type QueryRuleListRequest struct {
	Page    int                    `json:"page" validate:"gte=0"`
	PerPage int                    `json:"perPage" validate:"gt=0"`
	ChainID string                 `json:"chainID"` // 查询哪个chainID上绑定的rule，与tokenID二选一
	TokenID string                 `json:"tokenID"` // 查询哪个tokenID上绑定的rule，与chainID二选一
	Status  *fuseCommon.RuleStatus `json:"ruleStatus" validate:"omitempty,oneof=0 1"`
}

type QueryRuleListResponse struct {
	Total int64                 `json:"total"`
	Items []*ruleLogic.RuleItem `json:"items"`
}

type QueryRuleDetailRequest struct {
	RuleID int64 `json:"ruleID,string" validate:"gt=0"`
}

type CreateRuleRequest struct {
	RuleName   string                 `json:"ruleName" validate:"required"`
	Desc       string                 `json:"desc"`
	Priority   int64                  `json:"priority" validate:"gte=0,lte=100"`
	RuleStatus fuseCommon.RuleStatus  `json:"ruleStatus" validate:"oneof=0 1"`
	RuleDetail ruleModel.RuleDetail   `json:"ruleDetail" validate:"required"`
	Actions    ruleModel.ActionDetail `json:"actions"` // 规则内容
}

type CreateRuleResponse = ruleLogic.RuleItem

type DeleteRulesRequest struct {
	RuleIDs []string `json:"ruleIDs" validate:"required"`
}
type ModifyRulesRequest struct {
	Rules []*ModifyRuleInput `json:"rules" validate:"required"`
}

type ModifyRuleInput struct {
	RuleID     int64                   `json:"ruleID,string" validate:"gt=0"`
	RuleName   *string                 `json:"ruleName"`
	Desc       *string                 `json:"desc"`
	Priority   *int64                  `json:"priority"`
	RuleStatus *fuseCommon.RuleStatus  `json:"ruleStatus" validate:"omitempty,oneof=0 1"`
	RuleDetail *ruleModel.RuleDetail   `json:"ruleDetail"`
	Actions    *ruleModel.ActionDetail `json:"actions"`
}

type SupportParam struct {
	Name       string       `json:"paramName"`
	Value      string       `json:"paramValue"`
	SupportOps []*SupportOp `json:"supportOps"`
}

type SupportOp struct {
	Name             string                 `json:"opName"`
	Value            string                 `json:"opValue"`
	SupportValueType string                 `json:"supportValueType"`
	SupportValueEnum map[string]interface{} `json:"supportValueEnum"`
}
