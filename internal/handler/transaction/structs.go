/**
 * @note
 * struct
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package transaction

import (
	tLogic "gitlab.docsl.com/security/fuse/internal/logic/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type QueryTransactionListRequest struct {
	Page            int                         `json:"page" validate:"gte=0"`
	PerPage         int                         `json:"perPage" validate:"gt=0"`
	TransactionIDs  []int64                     `json:"transactionIDs"`
	StatementID     string                      `json:"statementID"`
	OrderID         string                      `json:"orderID"`
	WalletID        string                      `json:"walletID"`
	ChainID         string                      `json:"chainID"`
	TokenID         string                      `json:"tokenID"`
	TransactionHash string                      `json:"transactionHash"`
	TransferIndex   *int64                      `json:"transferIndex"`
	ToAddress       string                      `json:"toAddress"`
	Memo            string                      `json:"memo"`
	Type            *fuseCommon.TransactionType `json:"type"  validate:"omitempty,oneof=0 1"`
	ValidateStates  []fuseCommon.ValidateState  `json:"validateStates"`
}

type QueryTransactionListResponse struct {
	Total int64                     `json:"total"`
	Items []*tLogic.TransactionItem `json:"items"`
}

type SetTransactionValidateStateRequest struct {
	TransactionIDs []string                 `json:"transactionIDs" validate:"required"`
	ValidateState  fuseCommon.ValidateState `json:"validateState" validate:"oneof=0 1 2 3"`
}

type SetStatementValidateStateRequest struct {
	StatementIDs  []string                 `json:"statementIDs" validate:"required"`
	WalletID      string                   `json:"walletID" validate:"required"`
	ChainID       string                   `json:"chainID" validate:"required"`
	ValidateState fuseCommon.ValidateState `json:"validateState" validate:"oneof=0 1 2 3"`
}

type SetOrderValidateStateRequest struct {
	OrderIDs      []string                 `json:"orderIDs"`
	OrderType     fuseCommon.OrderType     `json:"orderType" validate:"oneof=deposit withdraw"`
	ValidateState fuseCommon.ValidateState `json:"validateState" validate:"oneof=0 1 2 3"`
}

type QueryOrderListRequest struct {
	Page               int                             `json:"page" validate:"gte=0"`
	PerPage            int                             `json:"perPage" validate:"gt=0"`
	OrderIDs           []int64                         `json:"orderIDs"`
	ChainID            string                          `json:"chainID"`
	TokenID            string                          `json:"tokenID"`
	TransactionHash    string                          `json:"transactionHash"`
	TransferIndex      *int64                          `json:"transferIndex"`
	ToAddress          string                          `json:"toAddress"`
	FormattedToAddress string                          `json:"formattedToAddress"`
	Memo               string                          `json:"memo"`
	OrderType          fuseCommon.OrderType            `json:"orderType" validate:"oneof=deposit withdraw"`
	DepositStates      []fuseCommon.DepositOrderState  `json:"depositStates"`
	WithdrawStates     []fuseCommon.WithdrawOrderState `json:"withdrawStates"`
}

type QueryOrderListResponse struct {
	Total int64               `json:"total"`
	Items []*tLogic.OrderItem `json:"items"`
}

type QueryStatementListRequest struct {
	Page            int                              `json:"page" validate:"gte=0"`
	PerPage         int                              `json:"perPage" validate:"gt=0"`
	StatementIDs    []string                         `json:"statementIDs"`
	ChainID         string                           `json:"chainID"`
	TokenID         string                           `json:"tokenID"`
	TransactionHash string                           `json:"transactionHash"`
	TransferIndex   *int64                           `json:"transferIndex"`
	ToAddress       string                           `json:"toAddress"`
	Memo            string                           `json:"memo"`
	BusinessType    fuseCommon.StatementBusinessType `json:"businessType" validate:"oneof=deposit withdraw"`
	State           *fuseCommon.StatementState       `json:"state"`
}

type QueryStatementListResponse struct {
	Total int64                   `json:"total"`
	Items []*tLogic.StatementItem `json:"items"`
}
