/**
 * @note
 * transaction
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package transaction

import (
	"github.com/kataras/iris/v12"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"

	. "gitlab.docsl.com/security/common"
	tLogic "gitlab.docsl.com/security/fuse/internal/logic/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryTransactionList 查询交易列表
func QueryTransactionList(ctx iris.Context) {
	req := &QueryTransactionListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	var err error
	ret := &QueryTransactionListResponse{}
	ret.Items, ret.Total, err = tLogic.QueryTransactionList(ctx, req.Page, req.PerPage,
		req.TransactionIDs, req.StatementID, req.OrderID, req.WalletID, req.ChainID, req.TokenID, req.TransactionHash,
		req.TransferIndex, req.ToAddress, req.Memo, req.Type, req.ValidateStates)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryTransactionList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

// SetTransactionValidateState 设置交易校验状态
func SetTransactionValidateState(ctx iris.Context) {
	req := &SetTransactionValidateStateRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	transactionIDs, errConvert := StringsToInt64s(req.TransactionIDs)
	if errConvert != nil {
		SetRet(ctx, NewError(ErrCodeParam, errConvert))
		return
	}

	err := tLogic.SetTransactionValidateState(ctx, transactionIDs, req.ValidateState)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSetTransactionValidateState, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationSetTransactionState, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

// SetStatementValidateState 设置流水校验状态
func SetStatementValidateState(ctx iris.Context) {
	req := &SetStatementValidateStateRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	err := tLogic.SetStatementValidateState(ctx, req.StatementIDs, req.WalletID, req.ChainID, req.ValidateState)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSetStatementValidateState, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationSetStatementState, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

// SetOrderValidateState 设置订单校验状态
func SetOrderValidateState(ctx iris.Context) {
	req := &SetOrderValidateStateRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}
	orderIDs, errConvert := StringsToInt64s(req.OrderIDs)
	if errConvert != nil {
		SetRet(ctx, NewError(ErrCodeParam, errConvert))
		return
	}

	err := tLogic.SetOrderValidateState(ctx, orderIDs, req.OrderType, req.ValidateState)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSetOrderValidateState, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationSetOrderState, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

// SyncOrders 同步订单
func SyncOrders(ctx iris.Context) {
	req := &SyncOrdersRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	totalSynced, latestTimeMilli, err := tLogic.SyncOrders(ctx, req.StartTimeMilli, req.EndTimeMilli, req.OrderType, req.ChainIDs)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSyncOrders, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationSyncOrders, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(&SyncOrdersResponse{
		TotalSynced:     totalSynced,
		LatestTimeMilli: latestTimeMilli,
	}))
}

// SyncStatements 同步流水
func SyncStatements(ctx iris.Context) {
	req := &SyncStatementsRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	totalSynced, latestTimeMilli, err := tLogic.SyncStatements(ctx, req.WalletID, req.ChainID, req.StartTimeMilli, req.EndTimeMilli)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrSyncStatements, err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, fuseCommon.OperationSyncStatements, &op_log.OperationDetail{
		Target: req,
	}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(&SyncStatementsResponse{
		TotalSynced:     totalSynced,
		LatestTimeMilli: latestTimeMilli,
	}))
}

// QueryOrderList 查询订单列表
func QueryOrderList(ctx iris.Context) {
	req := &QueryOrderListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	var err error
	ret := &QueryOrderListResponse{}
	ret.Items, ret.Total, err = tLogic.QueryOrderList(ctx, req.Page, req.PerPage, req.OrderIDs, req.ChainID,
		req.TokenID, req.TransactionHash, req.TransferIndex, req.ToAddress, req.FormattedToAddress, req.Memo, &req.OrderType,
		req.DepositStates, req.WithdrawStates)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryOrderList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}

// QueryStatementList 查询流水列表
func QueryStatementList(ctx iris.Context) {
	req := &QueryStatementListRequest{}
	errJson := ctx.ReadJSON(req)
	if errJson != nil {
		SetRet(ctx, NewError(ErrCodeJson, errJson))
		return
	}
	if errValidate := Validator.Struct(req); errValidate != nil {
		SetRet(ctx, NewError(ErrCodeParam, errValidate))
		return
	}

	var err error
	ret := &QueryStatementListResponse{}
	ret.Items, ret.Total, err = tLogic.QueryStatementList(ctx, req.Page, req.PerPage, req.StatementIDs, req.ChainID,
		req.TokenID, req.TransactionHash, req.TransferIndex, req.ToAddress, req.Memo, &req.BusinessType,
		req.State)
	if err != nil {
		SetRet(ctx, NewError(fuseCommon.ErrQueryStatementList, err))
		return
	}
	SetRet(ctx, NewError(ErrCodeOK).SetDesc(ret))
}
