/**
 * @note
 * user
 *
 * <AUTHOR>
 * @date 	2025-02-07
 */
package user

import (
	"encoding/base64"
	"errors"
	"strings"

	"github.com/kataras/iris/v12"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/cas"
	uModel "gitlab.docsl.com/security/fuse/pkg/model/user"
)

func GetUserInfo(ctx iris.Context) {
	ret := &UserResponse{User: common.GetUser(ctx)}
	tb, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		common.SetRet(ctx, common.NewError(common.ErrCodeSys, err))
		return
	} else if err == nil {
		c := make([]interface{}, 0)
		_ = common.JsonStringDecode(tb.WebauthnCredentials, &c)
		if len(c) > 0 {
			b, _ := common.JsonEncode(c[0])
			ret.WebauthnCredentials = base64.StdEncoding.EncodeToString(b)
		}
	}
	common.SetRet(ctx, common.NewError(common.ErrCodeOK).SetDesc(ret))
	return
}

func Callback(ctx iris.Context) {
	param := &cas.SSOQuery{}
	if err := ctx.ReadQuery(param); err != nil || param.JumpTo == common.StringEmpty {
		common.SetRet(ctx, common.NewError(common.ErrCodeParam, err))
		return
	}
	// 校验jumpTo合法性
	if !strings.HasPrefix(param.JumpTo, cas.GetConfig().AppDomain) {
		common.SetRet(ctx, common.NewErrorWithMessage(common.ErrCodeParam, "param jump_to is invalid"))
		return
	}
	ctx.Redirect(param.JumpTo)
}

func Logout(ctx iris.Context) {
	common.SetRet(ctx, common.NewError(common.ErrCodeOK))
}
