/**
 * @note
 * webauthn
 *
 * <AUTHOR>
 * @date 	2025-03-03
 */
package user

import (
	"io"

	"github.com/kataras/iris/v12"

	. "gitlab.docsl.com/security/common"
	waLogic "gitlab.docsl.com/security/fuse/internal/logic/webauthn"
	"gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/model/op_log"
)

func WebAuthnSignupBegin(ctx iris.Context) {
	options, err := waLogic.WebauthnRegisterBegin(ctx)
	if err != nil {
		SetRet(ctx, NewError(common.ErrWebAuthnSignupBegin, err))
		return
	}
	retBytes, err := JsonEncode(options)
	if err != nil {
		SetRet(ctx, NewError(common.ErrWebAuthnSignupBegin, err))
		return
	}
	SetRet(ctx, retBytes)
}

func WebAuthnSignupFinish(ctx iris.Context) {
	body, err := ctx.GetBody()
	if err != nil && err != io.EOF {
		SetRet(ctx, NewError(common.ErrWebAuthnSignupFinish, err))
		return
	}
	err = waLogic.WebauthnRegisterFinish(ctx, body)
	if err != nil {
		SetRet(ctx, NewErrorWithMessage(common.ErrWebAuthnSignupFinish, err.Error(), err))
		return
	}
	if err = op_log.CreateOperationLog(ctx, common.OperationWebAuthnRegister, &op_log.OperationDetail{}); err != nil {
		GetLogger(ctx).Errorf("Create operation log error: %v", err)
	}
	SetRet(ctx, NewError(ErrCodeOK))
}

func WebAuthnValidateBegin(ctx iris.Context) {
	options, err := waLogic.WebauthnValidateBegin(ctx)
	if err != nil {
		SetRet(ctx, NewError(common.ErrWebAuthnSignupBegin, err))
		return
	}
	retBytes, err := JsonEncode(options)
	if err != nil {
		SetRet(ctx, NewError(common.ErrWebAuthnSignupBegin, err))
		return
	}
	SetRet(ctx, retBytes)
}
