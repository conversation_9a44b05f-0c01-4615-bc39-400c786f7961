/**
 * @note
 * chain
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	"context"
	set "github.com/deckarep/golang-set/v2"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/fuse/pkg/helper/notify"

	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
)

func QueryChainList(ctx context.Context, page, perPage int, chainIDs []string, fuseType *fuseCommon.FuseType,
	fuseStatus *fuseCommon.FuseStatus) (items []*ChainItem, count int64, err error) {
	filter := assetsModel.QueryChainFilter{
		Page:       page,
		PerPage:    perPage,
		ChainIDs:   chainIDs,
		FuseType:   fuseType,
		FuseStatus: fuseStatus,
	}
	tbs, err := assetsModel.QueryChainBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = assetsModel.QueryChainCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*ChainItem, 0, len(tbs))
	assetIDs := make([]int64, 0, len(tbs))
	for _, tb := range tbs {
		assetIDs = append(assetIDs, int64(tb.ID))
	}
	// 查询绑定关系
	ruleRelations, err := ruleModel.QueryFuseRuleRelationBySeveralConditions(ctx, ruleModel.QueryFuseRuleRelationFilter{
		AssetIDs:  assetIDs,
		AssetType: fuseCommon.AssetTypeChain,
	})
	if err != nil {
		return nil, 0, err
	}
	// 查询绑定的rule并拼装
	ruleIDSet := set.NewSet[int64]()
	assetIDRuleIDMap := make(map[int64][]int64)
	for _, ruleRelation := range ruleRelations {
		ruleIDSet.Add(ruleRelation.RuleID)
		assetIDRuleIDMap[ruleRelation.AssetID] = append(assetIDRuleIDMap[ruleRelation.AssetID], ruleRelation.RuleID)
	}
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs:      ruleIDSet.ToSlice(),
		SelectFields: []string{"id", "name", "status"},
	})
	if err != nil {
		return nil, 0, err
	}
	ruleIDRuleMap := make(map[int64]*ruleModel.RuleTable)
	for _, rule := range rules {
		ruleIDRuleMap[int64(rule.ID)] = rule
	}
	for _, tb := range tbs {
		var associatedRules []*ruleModel.RuleTable
		ruleIDs := assetIDRuleIDMap[int64(tb.ID)]
		for _, ruleID := range ruleIDs {
			if associatedRule := ruleIDRuleMap[ruleID]; associatedRule != nil {
				associatedRules = append(associatedRules, associatedRule)
			}
		}
		items = append(items, wrapChainTableToItem(tb, associatedRules))
	}
	return
}

func CreateChains(ctx context.Context, inputs []*CreateChainInput) (err error) {
	tbs := make([]*assetsModel.ChainTable, 0, len(inputs))
	for _, input := range inputs {
		tb := &assetsModel.ChainTable{
			ChainID:   input.ChainID,
			ChainName: input.ChainName,
			Desc:      input.Desc,
		}
		tb.ID = uint(idgen.GetID())
		tbs = append(tbs, tb)
	}
	// 创建chains
	createdChains, err := assetsModel.CreateChains(ctx, tbs)
	if err != nil {
		return err
	}
	// 处理规则绑定关系
	for i, chain := range createdChains {
		input := inputs[i]
		if len(input.RuleIDs) == 0 {
			continue
		}
		// 验证规则是否存在
		existingRules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
			RuleIDs: input.ruleIDs,
		})
		if err != nil {
			return err
		}

		// 创建规则映射以检查所有规则是否存在
		ruleMap := make(map[int64]bool)
		for _, rule := range existingRules {
			ruleMap[int64(rule.ID)] = true
		}

		// 验证所有规则是否存在
		var missingRuleIDs []int64
		for _, ruleID := range input.ruleIDs {
			if !ruleMap[ruleID] {
				missingRuleIDs = append(missingRuleIDs, ruleID)
			}
		}

		if len(missingRuleIDs) > 0 {
			return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
		}

		// 创建规则关联
		relations := make([]*ruleModel.RuleRelationTable, 0, len(input.RuleIDs))
		for _, ruleID := range input.ruleIDs {
			relations = append(relations, &ruleModel.RuleRelationTable{
				RuleID:    ruleID,
				AssetType: fuseCommon.AssetTypeChain,
				AssetID:   int64(chain.ID),
			})
		}

		_, err = ruleModel.CreateFuseRuleRelations(ctx, relations)
		if err != nil {
			return err
		}
	}

	return nil
}

func ModifyChains(ctx context.Context, inputs []*ModifyChainInput) (err error) {
	// 先处理Chain表更新
	fusedChainIDs, recoveredChainIDs := make([]string, 0), make([]string, 0) // 通知用
	defer func() {
		// 如果手动熔断，发送一个熔断消息
		err := notify.MessageManualFuse(ctx, fusedChainIDs, make([]string, 0), common.GetUser(ctx).Name, false)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
		// 如果手动熔断恢复，发送一个恢复消息
		err = notify.MessageManualFuse(ctx, recoveredChainIDs, make([]string, 0), common.GetUser(ctx).Name, true)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
	}()
	for _, input := range inputs {
		// 先查询chain的ID
		chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
			ChainIDs: []string{input.ChainID},
		})
		if err != nil {
			return err
		}
		if len(chains) == 0 {
			return common.ErrRecordNotFound
		}
		err = assetsModel.UpdateChainByChainID(ctx, input.ChainID, input.ChainName, input.Desc, input.FuseType, input.FuseStatus)
		if err != nil {
			return err
		}
		// 熔断通知
		if chains[0].FuseStatus == fuseCommon.FuseStatusFused && input.FuseStatus != nil && *input.FuseStatus == fuseCommon.FuseStatusNormal {
			recoveredChainIDs = append(recoveredChainIDs, input.ChainID) // 手动解除熔断
		} else if chains[0].FuseStatus == fuseCommon.FuseStatusNormal && input.FuseStatus != nil && *input.FuseStatus == fuseCommon.FuseStatusFused {
			fusedChainIDs = append(fusedChainIDs, input.ChainID) // 手动熔断
		}

		// 处理规则绑定关系，只有当传入了RuleIDs才处理
		if input.RuleIDs == nil {
			continue
		}

		chainID := int64(chains[0].ID)

		// 验证规则是否存在
		if len(input.RuleIDs) > 0 {
			existingRules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
				RuleIDs: input.ruleIDs,
			})
			if err != nil {
				return err
			}

			// 创建规则映射以检查所有规则是否存在
			ruleMap := make(map[int64]bool)
			for _, rule := range existingRules {
				ruleMap[int64(rule.ID)] = true
			}

			// 验证所有规则是否存在
			var missingRuleIDs []int64
			for _, ruleID := range input.ruleIDs {
				if !ruleMap[ruleID] {
					missingRuleIDs = append(missingRuleIDs, ruleID)
				}
			}

			if len(missingRuleIDs) > 0 {
				return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
			}
		}

		// 删除现有的规则关联
		_, err = ruleModel.DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx, fuseCommon.AssetTypeChain, []int64{chainID})
		if err != nil {
			return err
		}

		// 创建新的规则关联
		if len(input.RuleIDs) > 0 {
			relations := make([]*ruleModel.RuleRelationTable, 0, len(input.RuleIDs))
			for _, ruleID := range input.ruleIDs {
				relations = append(relations, &ruleModel.RuleRelationTable{
					RuleID:    ruleID,
					AssetType: fuseCommon.AssetTypeChain,
					AssetID:   chainID,
				})
			}

			_, err = ruleModel.CreateFuseRuleRelations(ctx, relations)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func DeleteChains(ctx context.Context, chainIDs []string) (err error) {
	// 先查询chains获取他们的内部ID
	chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
		ChainIDs: chainIDs,
	})
	if err != nil {
		return err
	}
	for _, chain := range chains { // 熔断状态下不允许删除
		if chain.FuseStatus == fuseCommon.FuseStatusFused {
			return fuseCommon.ErrModifyFusedAssetsNotAllowed(chain.ChainID)
		}
	}

	// 收集要删除的chain的内部ID
	var assetIDs []int64
	for _, chain := range chains {
		assetIDs = append(assetIDs, int64(chain.ID))
	}

	// 删除规则绑定关系
	if len(assetIDs) > 0 {
		_, err = ruleModel.DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx, fuseCommon.AssetTypeChain, assetIDs)
		if err != nil {
			return err
		}
	}

	// 删除chain记录
	_, err = assetsModel.DeleteChainsByChainIDs(ctx, chainIDs)
	return err
}

// SyncChains 同步链信息
// 从wallet服务获取所有支持的链信息，并将新的链信息写入数据库
// 对于已经存在的链，不会进行更新
func SyncChains(ctx context.Context) (int, error) {
	// 调用wallet服务获取所有支持的链信息
	chains, err := wallet.GetSupportedChains(ctx)
	if err != nil {
		return 0, err
	}

	// 如果没有链，直接返回
	if len(chains) == 0 {
		return 0, nil
	}

	// 收集所有链的chainID
	chainIDs := make([]string, 0, len(chains))
	for _, chain := range chains {
		chainIDs = append(chainIDs, chain.ChainID)
	}

	// 查询数据库中已经存在的链
	existingChains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
		ChainIDs: chainIDs,
	})
	if err != nil {
		return 0, err
	}

	// 创建已存在链的映射，用于快速查找
	existingChainMap := make(map[string]bool)
	for _, chain := range existingChains {
		existingChainMap[chain.ChainID] = true
	}

	// 筛选出需要新增的链
	newChains := make([]*assetsModel.ChainTable, 0)
	for _, chain := range chains {
		// 如果链已经存在，跳过
		if existingChainMap[chain.ChainID] {
			continue
		}

		// 创建新的链记录
		newChain := &assetsModel.ChainTable{
			ChainID:    chain.ChainID,
			ChainName:  chain.Symbol,
			Desc:       chain.Protocol,
			FuseType:   fuseCommon.FuseTypeManual,   // 默认为手动熔断
			FuseStatus: fuseCommon.FuseStatusNormal, // 默认为正常状态
		}

		newChains = append(newChains, newChain)
	}

	// 如果没有新增的链，直接返回
	if len(newChains) == 0 {
		return 0, nil
	}

	// 创建新的链记录
	_, err = assetsModel.CreateChains(ctx, newChains)
	if err != nil {
		return 0, err
	}

	return len(newChains), nil
}
