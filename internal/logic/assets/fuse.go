/**
 * @note
 * fuse
 *
 * <AUTHOR>
 * @date 	2025-05-07
 */
package assets

import (
	// 系统内置包
	"context"
	"errors"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryAssetsFuseState 查询资产熔断状态
func QueryAssetsFuseState(ctx context.Context, chainID, tokenID string) (bool, error) {

	// 查询chain的熔断状态
	chain, err := assetsModel.QueryChainByChainID(ctx, chainID)
	if err != nil && !errors.Is(err, common.ErrRecordNotFound) {
		return false, err
	}

	// 如果chain存在且处于熔断状态，返回熔断状态
	if chain != nil && chain.FuseStatus == fuseCommon.FuseStatusFused {
		return true, nil
	}

	// 如果指定了tokenID，查询token的熔断状态
	if tokenID != common.StringEmpty {
		token, err := assetsModel.QueryTokenByTokenID(ctx, tokenID)
		if err != nil && !errors.Is(err, common.ErrRecordNotFound) {
			return false, err
		}

		// 如果token存在且处于熔断状态，返回熔断状态
		if token != nil && token.FuseStatus == fuseCommon.FuseStatusFused {
			return true, nil
		}
	}

	return false, nil
}

// QueryAllFuseState 查询所有处于熔断状态的资产
func QueryAllFuseState(ctx context.Context) ([]*QueryAllFuseStateItem, error) {
	ret := make([]*QueryAllFuseStateItem, 0)

	// 查询所有处于熔断状态的chain
	fuseStatus := fuseCommon.FuseStatusFused
	chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
		FuseStatus: &fuseStatus,
	})
	if err != nil {
		return nil, err
	}

	// 添加到响应中
	for _, chain := range chains {
		ret = append(ret, &QueryAllFuseStateItem{
			ChainID: chain.ChainID,
		})
	}

	// 查询所有处于熔断状态的token
	tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
		FuseStatus: &fuseStatus,
	})
	if err != nil {
		return nil, err
	}

	// 添加到响应中
	for _, token := range tokens {
		ret = append(ret, &QueryAllFuseStateItem{
			ChainID: token.ChainID,
			TokenID: token.TokenID,
		})
	}

	return ret, nil
}
