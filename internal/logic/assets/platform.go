/**
 * @note
 * platform
 *
 * <AUTHOR>
 * @date 	2025-05-08
 */
package assets

import (
	// 系统内置包
	"context"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
	"time"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// PlatformItem 平台信息
type PlatformItem struct {
	WalletID   string                      `json:"walletID"`
	Name       string                      `json:"name"`
	Desc       string                      `json:"desc"`
	Config     *assetsModel.PlatformConfig `json:"config"`
	SyncStatus fuseCommon.SyncStatus       `json:"syncStatus"`
	CreatedAt  time.Time                   `json:"createdAt"`
	UpdatedAt  time.Time                   `json:"updatedAt"`
}

// CreatePlatformInput 创建平台输入
type CreatePlatformInput struct {
	WalletID   string                     `json:"walletID" validate:"required"`
	Name       string                     `json:"name" validate:"required"`
	Desc       string                     `json:"desc"`
	Config     assetsModel.PlatformConfig `json:"config"`
	SyncStatus fuseCommon.SyncStatus      `json:"syncStatus"`
}

// ModifyPlatformInput 修改平台输入
type ModifyPlatformInput struct {
	WalletID   string                      `json:"walletID" validate:"required"`
	Name       *string                     `json:"name,omitempty"`
	Desc       *string                     `json:"desc,omitempty"`
	Config     *assetsModel.PlatformConfig `json:"config,omitempty"`
	SyncStatus *fuseCommon.SyncStatus      `json:"syncStatus,omitempty"`
}

// wrapPlatformTableToItem 将PlatformTable转换为PlatformItem
func wrapPlatformTableToItem(tb *assetsModel.PlatformTable) *PlatformItem {
	if tb == nil {
		return &PlatformItem{}
	}
	return &PlatformItem{
		WalletID:   tb.WalletID,
		Name:       tb.Name,
		Desc:       tb.Desc,
		Config:     tb.Config,
		SyncStatus: tb.SyncStatus,
		CreatedAt:  tb.CreatedAt,
		UpdatedAt:  tb.UpdatedAt,
	}
}

// QueryPlatformList 查询平台列表
func QueryPlatformList(ctx context.Context, page, perPage int, walletIDs []string, syncStatus *fuseCommon.SyncStatus) (items []*PlatformItem, count int64, err error) {
	filter := assetsModel.QueryPlatformFilter{
		Page:       page,
		PerPage:    perPage,
		WalletIDs:  walletIDs,
		SyncStatus: syncStatus,
	}
	tbs, err := assetsModel.QueryPlatformBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = assetsModel.QueryPlatformCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*PlatformItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapPlatformTableToItem(tb))
	}
	return items, count, nil
}

// CreatePlatforms 创建平台
func CreatePlatforms(ctx context.Context, inputs []*CreatePlatformInput) (err error) {
	tbs := make([]*assetsModel.PlatformTable, 0, len(inputs))
	for _, input := range inputs {
		tb := &assetsModel.PlatformTable{
			WalletID:   input.WalletID,
			Name:       input.Name,
			Desc:       input.Desc,
			Config:     &input.Config,
			SyncStatus: input.SyncStatus,
		}
		tb.ID = uint(idgen.GetID())
		tbs = append(tbs, tb)
	}
	// 创建platforms
	_, err = assetsModel.CreatePlatforms(ctx, tbs)
	if err != nil {
		return err
	}
	return nil
}

// ModifyPlatforms 修改平台
func ModifyPlatforms(ctx context.Context, inputs []*ModifyPlatformInput) (err error) {
	// 处理Platform表更新
	for _, input := range inputs {
		// 先查询platform是否存在
		platforms, err := assetsModel.QueryPlatformBySeveralConditions(ctx, assetsModel.QueryPlatformFilter{
			WalletIDs: []string{input.WalletID},
		})
		if err != nil {
			return err
		}
		if len(platforms) == 0 {
			return common.ErrRecordNotFound
		}
		err = assetsModel.UpdatePlatformByWalletID(ctx, input.WalletID, input.Name, input.Desc, input.Config, input.SyncStatus)
		if err != nil {
			return err
		}
	}
	return nil
}

// DeletePlatforms 删除平台
func DeletePlatforms(ctx context.Context, walletIDs []string) (err error) {
	// 先查询platforms是否存在
	platforms, err := assetsModel.QueryPlatformBySeveralConditions(ctx, assetsModel.QueryPlatformFilter{
		WalletIDs: walletIDs,
	})
	if err != nil {
		return err
	}
	if len(platforms) == 0 {
		return common.ErrRecordNotFound
	}
	// 删除platform记录
	_, err = assetsModel.DeletePlatformsByWalletIDs(ctx, walletIDs)
	return err
}

// SyncPlatforms 同步平台信息
// 从 wallet 服务获取所有支持的钱包信息，并将新的钱包信息写入数据库
// 对于已经存在的钱包，不会进行更新
func SyncPlatforms(ctx context.Context) (int, error) {
	// 调用wallet服务获取所有支持的钱包信息
	wallets, err := wallet.GetWalletList(ctx)
	if err != nil {
		return 0, err
	}

	// 如果没有钱包，直接返回
	if len(wallets) == 0 {
		return 0, nil
	}

	// 收集所有钱包的walletID
	walletIDs := make([]string, 0, len(wallets))
	for _, w := range wallets {
		walletIDs = append(walletIDs, w.WalletID)
	}

	// 查询数据库中已经存在的钱包
	existingPlatforms, err := assetsModel.QueryPlatformBySeveralConditions(ctx, assetsModel.QueryPlatformFilter{
		WalletIDs: walletIDs,
	})
	if err != nil {
		return 0, err
	}

	// 创建已存在钱包的映射，用于快速查找
	existingPlatformMap := make(map[string]bool)
	for _, platform := range existingPlatforms {
		existingPlatformMap[platform.WalletID] = true
	}

	// 收集需要创建的新钱包
	newPlatforms := make([]*assetsModel.PlatformTable, 0)
	for _, w := range wallets {
		// 如果钱包已经存在，跳过
		if existingPlatformMap[w.WalletID] {
			continue
		}

		// 创建新的钱包记录
		newPlatform := &assetsModel.PlatformTable{
			WalletID:   w.WalletID,
			Name:       w.Name,
			Desc:       w.OrgID, // 将 OrgID 设置为描述
			Config:     &assetsModel.PlatformConfig{},
			SyncStatus: fuseCommon.SyncStatusOff, // 默认不开启同步
		}
		newPlatforms = append(newPlatforms, newPlatform)
	}

	// 如果没有新的钱包需要创建，直接返回
	if len(newPlatforms) == 0 {
		return 0, nil
	}

	// 创建新的钱包记录
	_, err = assetsModel.CreatePlatforms(ctx, newPlatforms)
	if err != nil {
		return 0, err
	}

	return len(newPlatforms), nil
}
