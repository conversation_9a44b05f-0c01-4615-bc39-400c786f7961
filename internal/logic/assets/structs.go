/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	"time"

	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type TokenItem struct {
	TokenID    string                `json:"tokenID"`
	ChainID    string                `json:"chainID"`
	TokenName  string                `json:"tokenName"`
	Desc       string                `json:"desc"`
	Decimals   int64                 `json:"decimals"`
	FuseType   fuseCommon.FuseType   `json:"fuseType"`
	FuseStatus fuseCommon.FuseStatus `json:"fuseStatus"`
	CreatedAt  time.Time             `json:"createdAt"`
	UpdatedAt  time.Time             `json:"updatedAt"`

	Rule []*AssociateRule `json:"rule,omitempty"` // 绑定在资产上的规则
}

type AssociateRule struct {
	RuleID     int64                 `json:"ruleID,string"`
	RuleName   string                `json:"ruleName"`
	RuleStatus fuseCommon.RuleStatus `json:"ruleStatus"`
}

func wrapTokenTableToItem(tb *assetsModel.TokenTable, associateRules []*ruleModel.RuleTable) *TokenItem {
	if tb == nil {
		return &TokenItem{}
	}
	ret := &TokenItem{
		TokenID:    tb.TokenID,
		ChainID:    tb.ChainID,
		TokenName:  tb.TokenName,
		Desc:       tb.Desc,
		Decimals:   tb.Decimals,
		FuseType:   tb.FuseType,
		FuseStatus: tb.FuseStatus,
		CreatedAt:  tb.CreatedAt,
		UpdatedAt:  tb.UpdatedAt,
	}
	if len(associateRules) > 0 {
		for _, associateRule := range associateRules {
			ret.Rule = append(ret.Rule, &AssociateRule{
				RuleID:     int64(associateRule.ID),
				RuleName:   associateRule.Name,
				RuleStatus: associateRule.Status,
			})
		}
	}
	return ret
}

type ChainItem struct {
	ChainID    string                `json:"chainID"`
	ChainName  string                `json:"chainName"`
	Desc       string                `json:"desc"`
	FuseType   fuseCommon.FuseType   `json:"fuseType"`
	FuseStatus fuseCommon.FuseStatus `json:"fuseStatus"`
	CreatedAt  time.Time             `json:"createdAt"`
	UpdatedAt  time.Time             `json:"updatedAt"`

	Rule []*AssociateRule `json:"rule,omitempty"` // 绑定在资产上的规则
}

func wrapChainTableToItem(tb *assetsModel.ChainTable, associateRules []*ruleModel.RuleTable) *ChainItem {
	if tb == nil {
		return &ChainItem{}
	}
	ret := &ChainItem{
		ChainID:    tb.ChainID,
		ChainName:  tb.ChainName,
		Desc:       tb.Desc,
		FuseType:   tb.FuseType,
		FuseStatus: tb.FuseStatus,
		CreatedAt:  tb.CreatedAt,
		UpdatedAt:  tb.UpdatedAt,
	}
	if len(associateRules) > 0 {
		for _, associateRule := range associateRules {
			ret.Rule = append(ret.Rule, &AssociateRule{
				RuleID:     int64(associateRule.ID),
				RuleName:   associateRule.Name,
				RuleStatus: associateRule.Status,
			})
		}
	}
	return ret
}

type CreateChainInput struct {
	ChainID   string `json:"chainID" validate:"required"`
	ChainName string `json:"chainName" validate:"required"`
	Desc      string `json:"desc"`

	RuleIDs []string `json:"ruleIDs"`
	ruleIDs []int64
}

func (input *CreateChainInput) ParseRuleIDs() (err error) {
	input.ruleIDs, err = common.StringsToInt64s(input.RuleIDs)
	return err
}

type ModifyChainInput struct {
	ChainID    string                 `json:"chainID" validate:"required"`
	ChainName  *string                `json:"chainName"`
	Desc       *string                `json:"desc"`
	FuseType   *fuseCommon.FuseType   `json:"fuseType"`
	FuseStatus *fuseCommon.FuseStatus `json:"fuseStatus"`

	RuleIDs []string `json:"ruleIDs"`
	ruleIDs []int64
}

func (input *ModifyChainInput) ParseRuleIDs() (err error) {
	input.ruleIDs, err = common.StringsToInt64s(input.RuleIDs)
	return err
}

type CreateTokenInput struct {
	TokenID   string `json:"tokenID" validate:"required"`
	ChainID   string `json:"chainID" validate:"required"`
	TokenName string `json:"tokenName" validate:"required"`
	Decimals  int64  `json:"decimals" validate:"gte=0"`
	Desc      string `json:"desc"`

	RuleIDs []string `json:"ruleIDs"`
	ruleIDs []int64
}

func (input *CreateTokenInput) ParseRuleIDs() (err error) {
	input.ruleIDs, err = common.StringsToInt64s(input.RuleIDs)
	return err
}

type ModifyTokenInput struct {
	TokenID    string                 `json:"tokenID" validate:"required"`
	ChainID    *string                `json:"chainID"`
	TokenName  *string                `json:"tokenName"`
	Desc       *string                `json:"desc"`
	Decimals   *int64                 `json:"decimals"`
	FuseType   *fuseCommon.FuseType   `json:"fuseType"`
	FuseStatus *fuseCommon.FuseStatus `json:"fuseStatus"`

	RuleIDs []string `json:"ruleIDs"`
	ruleIDs []int64
}

func (input *ModifyTokenInput) ParseRuleIDs() (err error) {
	input.ruleIDs, err = common.StringsToInt64s(input.RuleIDs)
	return err
}

type QueryAllFuseStateItem struct {
	ChainID string `json:"chainID,omitempty"`
	TokenID string `json:"tokenId,omitempty"`
}
