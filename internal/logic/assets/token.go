/**
 * @note
 * token
 *
 * <AUTHOR>
 * @date 	2025-04-17
 */
package assets

import (
	"context"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/fuse/pkg/helper/notify"
	"time"

	set "github.com/deckarep/golang-set/v2"

	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
)

func QueryTokenList(ctx context.Context, page, perPage int, tokenIDs []string, chainIDs []string, fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (items []*TokenItem, count int64, err error) {
	filter := assetsModel.QueryTokenFilter{
		Page:       page,
		PerPage:    perPage,
		TokenIDs:   tokenIDs,
		ChainIDs:   chainIDs,
		FuseType:   fuseType,
		FuseStatus: fuseStatus,
	}
	tbs, err := assetsModel.QueryTokenBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = assetsModel.QueryTokenCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*TokenItem, 0, len(tbs))
	assetIDs := make([]int64, 0, len(tbs))
	for _, tb := range tbs {
		assetIDs = append(assetIDs, int64(tb.ID))
	}
	// 查询绑定关系
	ruleRelations, err := ruleModel.QueryFuseRuleRelationBySeveralConditions(ctx, ruleModel.QueryFuseRuleRelationFilter{
		AssetIDs:  assetIDs,
		AssetType: fuseCommon.AssetTypeToken,
	})
	if err != nil {
		return nil, 0, err
	}
	// 查询绑定的rule并拼装
	ruleIDSet := set.NewSet[int64]()
	assetIDRuleIDMap := make(map[int64][]int64)
	for _, ruleRelation := range ruleRelations {
		ruleIDSet.Add(ruleRelation.RuleID)
		assetIDRuleIDMap[ruleRelation.AssetID] = append(assetIDRuleIDMap[ruleRelation.AssetID], ruleRelation.RuleID)
	}
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs:      ruleIDSet.ToSlice(),
		SelectFields: []string{"id", "name", "status"},
	})
	if err != nil {
		return nil, 0, err
	}
	ruleIDRuleMap := make(map[int64]*ruleModel.RuleTable)
	for _, rule := range rules {
		ruleIDRuleMap[int64(rule.ID)] = rule
	}
	for _, tb := range tbs {
		var associatedRules []*ruleModel.RuleTable
		ruleIDs := assetIDRuleIDMap[int64(tb.ID)]
		for _, ruleID := range ruleIDs {
			if associatedRule := ruleIDRuleMap[ruleID]; associatedRule != nil {
				associatedRules = append(associatedRules, associatedRule)
			}
		}
		items = append(items, wrapTokenTableToItem(tb, associatedRules))
	}
	return
}

func CreateTokens(ctx context.Context, inputs []*CreateTokenInput) (err error) {
	tbs := make([]*assetsModel.TokenTable, 0, len(inputs))
	for _, input := range inputs {
		tb := &assetsModel.TokenTable{
			TokenID:   input.TokenID,
			ChainID:   input.ChainID,
			TokenName: input.TokenName,
			Desc:      input.Desc,
			Decimals:  input.Decimals,
		}
		tbs = append(tbs, tb)
	}
	// 创建tokens
	createdTokens, err := assetsModel.CreateTokens(ctx, tbs)
	if err != nil {
		return err
	}

	// 处理规则绑定关系
	for i, token := range createdTokens {
		input := inputs[i]
		if len(input.RuleIDs) == 0 {
			continue
		}
		// 验证规则是否存在
		existingRules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
			RuleIDs: input.ruleIDs,
		})
		if err != nil {
			return err
		}

		// 创建规则映射以检查所有规则是否存在
		ruleMap := make(map[int64]bool)
		for _, rule := range existingRules {
			ruleMap[int64(rule.ID)] = true
		}

		// 验证所有规则是否存在
		var missingRuleIDs []int64
		for _, ruleID := range input.ruleIDs {
			if !ruleMap[ruleID] {
				missingRuleIDs = append(missingRuleIDs, ruleID)
			}
		}

		if len(missingRuleIDs) > 0 {
			return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
		}

		// 创建规则关联
		relations := make([]*ruleModel.RuleRelationTable, 0, len(input.RuleIDs))
		for _, ruleID := range input.ruleIDs {
			relations = append(relations, &ruleModel.RuleRelationTable{
				RuleID:    ruleID,
				AssetType: fuseCommon.AssetTypeToken,
				AssetID:   int64(token.ID),
			})
		}

		_, err = ruleModel.CreateFuseRuleRelations(ctx, relations)
		if err != nil {
			return err
		}
	}

	return nil
}

func ModifyTokens(ctx context.Context, inputs []*ModifyTokenInput) (err error) {
	// 先处理Token表更新
	fusedTokenIDs, recoveredTokenIDs := make([]string, 0), make([]string, 0) // 通知用
	defer func() {
		// 如果手动熔断，发送一个熔断消息
		err := notify.MessageManualFuse(ctx, make([]string, 0), fusedTokenIDs, common.GetUser(ctx).Name, false)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
		// 如果手动熔断恢复，发送一个恢复消息
		err = notify.MessageManualFuse(ctx, make([]string, 0), recoveredTokenIDs, common.GetUser(ctx).Name, true)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
	}()
	for _, input := range inputs {
		// 先查询token的ID
		tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
			TokenIDs: []string{input.TokenID},
		})
		if err != nil {
			return err
		}
		if len(tokens) == 0 {
			return common.ErrRecordNotFound
		}

		err = assetsModel.UpdateTokenByTokenID(ctx, input.TokenID, input.ChainID, input.TokenName, input.Desc, input.Decimals, input.FuseType, input.FuseStatus)
		if err != nil {
			return err
		}

		// 熔断通知
		if tokens[0].FuseStatus == fuseCommon.FuseStatusFused && input.FuseStatus != nil && *input.FuseStatus == fuseCommon.FuseStatusNormal {
			recoveredTokenIDs = append(recoveredTokenIDs, input.TokenID) // 手动解除熔断
		} else if tokens[0].FuseStatus == fuseCommon.FuseStatusNormal && input.FuseStatus != nil && *input.FuseStatus == fuseCommon.FuseStatusFused {
			fusedTokenIDs = append(fusedTokenIDs, input.TokenID) // 手动熔断
		}

		// 处理规则绑定关系，只有当传入了RuleIDs才处理
		if input.RuleIDs == nil {
			continue
		}

		tokenID := int64(tokens[0].ID)

		// 验证规则是否存在
		if len(input.RuleIDs) > 0 {
			existingRules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
				RuleIDs: input.ruleIDs,
			})
			if err != nil {
				return err
			}

			// 创建规则映射以检查所有规则是否存在
			ruleMap := make(map[int64]bool)
			for _, rule := range existingRules {
				ruleMap[int64(rule.ID)] = true
			}

			// 验证所有规则是否存在
			var missingRuleIDs []int64
			for _, ruleID := range input.ruleIDs {
				if !ruleMap[ruleID] {
					missingRuleIDs = append(missingRuleIDs, ruleID)
				}
			}

			if len(missingRuleIDs) > 0 {
				return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
			}
		}

		// 删除现有的规则关联
		_, err = ruleModel.DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx, fuseCommon.AssetTypeToken, []int64{tokenID})
		if err != nil {
			return err
		}

		// 创建新的规则关联
		if len(input.RuleIDs) > 0 {
			relations := make([]*ruleModel.RuleRelationTable, 0, len(input.RuleIDs))
			for _, ruleID := range input.ruleIDs {
				relations = append(relations, &ruleModel.RuleRelationTable{
					RuleID:    ruleID,
					AssetType: fuseCommon.AssetTypeToken,
					AssetID:   tokenID,
				})
			}

			_, err = ruleModel.CreateFuseRuleRelations(ctx, relations)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func DeleteTokens(ctx context.Context, tokenIDs []string) (err error) {
	// 先查询tokens获取他们的内部ID
	tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
		TokenIDs: tokenIDs,
	})
	if err != nil {
		return err
	}

	for _, token := range tokens { // 熔断状态下不允许删除
		if token.FuseStatus == fuseCommon.FuseStatusFused {
			return fuseCommon.ErrModifyFusedAssetsNotAllowed(token.TokenID)
		}
	}
	// 构建tokenID到内部ID的映射
	tokenIDMap := make(map[string]int64)
	for _, token := range tokens {
		if token.TokenID != common.StringEmpty {
			tokenIDMap[token.TokenID] = int64(token.ID)
		}
	}

	// 收集要删除的token的内部ID
	var assetIDs []int64
	for _, tokenID := range tokenIDs {
		if id, exists := tokenIDMap[tokenID]; exists {
			assetIDs = append(assetIDs, id)
		}
	}

	// 删除规则绑定关系
	if len(assetIDs) > 0 {
		_, err = ruleModel.DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx, fuseCommon.AssetTypeToken, assetIDs)
		if err != nil {
			return err
		}
	}

	// 删除token记录
	_, err = assetsModel.DeleteTokensByTokenIDs(ctx, tokenIDs)
	return err
}

func SyncTokens(ctx context.Context, chainIDToSync string) (err error) {
	var existTokens []*assetsModel.TokenTable
	// 先查询tokens获取他们的内部ID
	if chainIDToSync == "all" { // 查所有
		existTokens, err = assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{})
	} else {
		existTokens, err = assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
			ChainIDs: []string{chainIDToSync},
		})
	}
	if err != nil {
		return err
	}

	// 构建tokenID到内部ID的映射
	tokenIDMap := make(map[string]int64)
	for _, token := range existTokens {
		if token.TokenID != common.StringEmpty {
			tokenIDMap[token.TokenID] = int64(token.ID)
		}
	}
	chainIDs := make([]string, 0)
	if chainIDToSync == "all" { // 同步所有chain
		allChains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{})
		if err != nil {
			return err
		}
		for _, chain := range allChains {
			chainIDs = append(chainIDs, chain.ChainID)
		}
	} else { // 否则只同步选定的chain
		chainIDs = append(chainIDs, chainIDToSync)
	}
	// 每个chain同步下
	for _, chainID := range chainIDs {
		// 通过调用钱包接口获取chain下的所有token
		currentTokens, err := wallet.GetSupportedTokens(ctx, chainID)
		if err != nil {
			return err
		}

		// 找出所有表里不存在的token
		timeStr := time.Now().Format(time.RFC3339)
		tokensToCreate := make([]*assetsModel.TokenTable, 0)
		for _, tokenToCreate := range currentTokens {
			if _, ok := tokenIDMap[tokenToCreate.TokenID]; !ok {
				t := &assetsModel.TokenTable{
					TokenID:   tokenToCreate.TokenID,
					ChainID:   tokenToCreate.ChainID,
					TokenName: tokenToCreate.Name,
					Decimals:  tokenToCreate.Decimals,
					Desc:      timeStr + "自动同步",
				}
				t.ID = uint(idgen.GetID())
				tokensToCreate = append(tokensToCreate, t)
			}
		}

		// 创建tokens
		if len(tokensToCreate) > 0 {
			_, err = assetsModel.CreateTokens(ctx, tokensToCreate)
		}
		if err != nil {
			return err
		}
	}
	return nil
}
