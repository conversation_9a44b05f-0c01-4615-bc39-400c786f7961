/**
 * @note
 * executor
 *
 * <AUTHOR>
 * @date 	2025-04-09
 */
package executor

import (
	// 系统内置包
	"context"
	"fmt"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_server"
	"strings"

	set "github.com/deckarep/golang-set/v2"

	// gitlab.docsl.com的包
	notifyHelper "gitlab.docsl.com/security/common_helper/notify"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	"gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/notify"
)

func ExecuteRuleAction(ctx context.Context, abnormalUnits []*transaction.TransactionUnitTable) (err error) {
	// 过一遍所有的units，统计ruleIDs，并查询出这些rule
	ruleIDSet := set.NewSet[int64]()
	for _, abnormalUnit := range abnormalUnits {
		ruleIDSet.Add(abnormalUnit.HitRuleID)
	}

	var rules []*ruleModel.RuleTable

	if !ruleIDSet.IsEmpty() {
		// 查询规则
		rules, err = ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
			RuleIDs: ruleIDSet.ToSlice(),
		})
		if err != nil {
			return fmt.Errorf("execute actions query rules error: %w", err)
		}
	}
	// 将规则按ID索引
	ruleMap := make(map[int64]*ruleModel.RuleTable)
	for _, rule := range rules {
		ruleMap[int64(rule.ID)] = rule
	}

	// Step 1 处理熔断
	err = Fuse(ctx, abnormalUnits, ruleMap)
	if err != nil {
		return fmt.Errorf("execute actions fuse error: %w", err)
	}

	// Step 2 处理消息通知
	err = MessageNotice(ctx, abnormalUnits, ruleMap)
	if err != nil {
		return fmt.Errorf("execute actions message alert error: %w", err)
	}
	return nil
}

func Fuse(ctx context.Context, abnormalUnits []*transaction.TransactionUnitTable, ruleMap map[int64]*ruleModel.RuleTable) error {
	// Step 1，再过一遍所有的units，读取其触发的rule的配置
	// 记录需要熔断的chain和token
	fuseChainMap := make(map[string]bool)            // 需要熔断的chain
	fuseTokenMap := make(map[string]map[string]bool) // 需要熔断的token，外层key是token_id，内层key是chain_id

	for _, unit := range abnormalUnits {

		rule, exists := ruleMap[unit.HitRuleID]
		if !exists || rule.Actions == nil {
			continue
		}

		// 如果配置了熔断链
		if rule.Actions.FuseChain {
			fuseChainMap[unit.ChainID] = true
		}

		// 如果配置了熔断token
		if rule.Actions.FuseToken {
			if fuseTokenMap[unit.TokenID] == nil {
				fuseTokenMap[unit.TokenID] = make(map[string]bool)
			}
			fuseTokenMap[unit.TokenID][unit.ChainID] = true
		}
	}

	fusedChainIDs, fusedTokenIDs := make([]string, 0), make([]string, 0)
	recoveredChainIDs, recoveredTokenIDs := make([]string, 0), make([]string, 0)
	defer func() {
		// 如果自动熔断，发送一个熔断消息
		err := notify.MessageAutoFuse(ctx, fusedChainIDs, fusedTokenIDs, false)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
		// 如果自动熔断恢复，发送一个恢复消息
		err = notify.MessageAutoFuse(ctx, recoveredChainIDs, recoveredTokenIDs, true)
		if err != nil {
			common.GetLogger(ctx).Errorln("send recover message error:", err)
		}
	}()

	// Step 2，查询所有的chain表和token表
	// 查询所有的chain
	allChains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{})
	if err != nil {
		return fmt.Errorf("query all chains error: %w", err)
	}

	// 查询所有的token
	allTokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{})
	if err != nil {
		return fmt.Errorf("query all tokens error: %w", err)
	}

	// 处理chain的熔断状态
	for _, chain := range allChains {
		// 如果不是自动熔断类型，忽略
		if chain.FuseType == fuseCommon.FuseTypeManual {
			continue
		}
		// 如果在需要熔断的链中，则设置为熔断状态
		if fuseChainMap[chain.ChainID] {
			// 如果当前不是熔断状态，则更新为熔断状态
			if chain.FuseStatus != fuseCommon.FuseStatusFused {
				fuseStatus := fuseCommon.FuseStatusFused
				err := assetsModel.UpdateChainByChainID(ctx, chain.ChainID, nil, nil, nil, &fuseStatus)
				if err != nil {
					return fmt.Errorf("update chain %s fuse status error: %w", chain.ChainID, err)
				}
				fusedChainIDs = append(fusedChainIDs, chain.ChainID)
			}
		} else {
			// 如果不在需要熔断的链中，且当前是熔断状态，则更新为非熔断状态
			if chain.FuseStatus == fuseCommon.FuseStatusFused {
				fuseStatus := fuseCommon.FuseStatusNormal
				err := assetsModel.UpdateChainByChainID(ctx, chain.ChainID, nil, nil, nil, &fuseStatus)
				if err != nil {
					return fmt.Errorf("update chain %s fuse status error: %w", chain.ChainID, err)
				}
				recoveredChainIDs = append(recoveredChainIDs, chain.ChainID)
			}
		}
	}

	// 处理token的熔断状态
	for _, token := range allTokens {
		// 如果不是自动熔断类型，忽略
		if token.FuseType == fuseCommon.FuseTypeManual {
			continue
		}
		// 如果在需要熔断的token中
		if chainMap, exists := fuseTokenMap[token.TokenID]; exists && chainMap[token.ChainID] {
			// 如果当前不是熔断状态，则更新为熔断状态
			if token.FuseStatus != fuseCommon.FuseStatusFused {
				fuseStatus := fuseCommon.FuseStatusFused
				err := assetsModel.UpdateTokenByTokenID(ctx, token.TokenID, nil, nil, nil, nil, nil, &fuseStatus)
				if err != nil {
					return fmt.Errorf("update token %s fuse status error: %w", token.TokenID, err)
				}
				fusedTokenIDs = append(fusedTokenIDs, token.TokenID)
			}
		} else {
			// 如果不在需要熔断的token中，且当前是熔断状态，则更新为非熔断状态
			if token.FuseStatus == fuseCommon.FuseStatusFused {
				fuseStatus := fuseCommon.FuseStatusNormal
				err := assetsModel.UpdateTokenByTokenID(ctx, token.TokenID, nil, nil, nil, nil, nil, &fuseStatus)
				if err != nil {
					return fmt.Errorf("update token %s fuse status error: %w", token.TokenID, err)
				}
				recoveredTokenIDs = append(recoveredTokenIDs, token.TokenID)
			}
		}
	}

	return nil
}

func MessageNotice(ctx context.Context, abnormalUnits []*transaction.TransactionUnitTable, ruleMap map[int64]*ruleModel.RuleTable) (err error) {
	var message strings.Builder
	var needSend bool
	for _, unit := range abnormalUnits {
		rule, exists := ruleMap[unit.HitRuleID]
		if !exists || rule.Actions == nil {
			continue
		}

		if rule.Actions.Message { // 需要消息报警
			message.WriteString(fmt.Sprintf("规则：%s，链：%s，币种：%s，类型：%s，订单号:%v，流水号：%v\n", rule.Name, unit.ChainID, unit.TokenID, fuseCommon.TransactionTypeNameMap[unit.Type], unit.OrderIDs, unit.StatementIDs))
			needSend = true
		}
	}
	if needSend {
		content := &notifyHelper.NotificationContent{
			Title:   fmt.Sprintf("流水比对异常[%s]", http_server.GetConfig().Environment),
			Message: message.String(),
		}
		errTeams := notify.SendTeamsMessage(ctx, notify.NotifyGroupAlarm, content, true)
		if errTeams != nil {
			common.GetLogger(ctx).Errorln("send teams message error:", errTeams)
			err = errTeams
		}
		errFeishu := notify.SendFeishuMessage(ctx, notify.NotifyGroupAlarm, content, true)
		if errFeishu != nil {
			common.GetLogger(ctx).Errorln("send feishu message error:", errFeishu)
			err = errFeishu
		}
	}
	return
}
