/**
 * @note
 * validator
 *
 * <AUTHOR>
 * @date 	2025-04-08
 */
package executor

import (
	"context"
	"errors"
	"math/big"
	"time"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/fuse/internal/logic/rule_engine"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func ValidateUnits(ctx context.Context) (abnormalUnits []*tModel.TransactionUnitTable, err error) {
	// 获取一批未validate及validate失败的unit
	var unitsToBeValidated []*tModel.TransactionUnitTable
	for page := 1; ; page++ {
		units, err := tModel.QueryTransactionUnitsBySeveralConditions(ctx, tModel.QueryTransactionUnitFilter{
			Page:           page,
			PerPage:        fuseCommon.ValidateTransactionUnitBatchSize,
			ValidateStates: []fuseCommon.ValidateState{fuseCommon.ValidateStateToBeValidated, fuseCommon.ValidateStateAbnormal},
		})
		if err != nil {
			return nil, err
		}
		unitsToBeValidated = append(unitsToBeValidated, units...)
		if len(units) < fuseCommon.ValidateTransactionUnitBatchSize {
			break
		}
	}
	// 每个unit执行一下validate
	for _, unit := range unitsToBeValidated {
		var hit *bool
		var hitRuleID int64
		var errValidate error
		switch unit.Type {
		case fuseCommon.TransactionTypeDeposit:
			hit, hitRuleID, errValidate = validateDepositUnit(ctx, unit)
		case fuseCommon.TransactionTypeWithdraw:
			hit, hitRuleID, errValidate = validateWithdrawUnit(ctx, unit)
		}
		if errValidate != nil {
			// validate有问题，可能是某些规则配置的不对导致，目前先打个日志，不中断，继续后面逻辑
			GetLogger(ctx).Errorf("validate unit fail, unitID: %d, err: %v", unit.ID, errValidate)
		}
		if hit == nil { // hit为nil的时候，表示条件并没有完全满足，需要之后继续校验
			continue
		}
		if *hit {
			unit.HitRuleID = hitRuleID
			unit.ValidateState = fuseCommon.ValidateStateAbnormal
			if unit.Type == fuseCommon.TransactionTypeDeposit {
				err = tModel.UpdateDepositTransactionUnitByID(ctx, int64(unit.ID), &unit.ValidateState, &unit.HitRuleID, nil, nil, nil)
			} else {
				err = tModel.UpdateWithdrawTransactionUnitByID(ctx, int64(unit.ID), &unit.ValidateState, &unit.HitRuleID, nil, nil, nil)
			}
			if err != nil {
				return abnormalUnits, err
			}
			abnormalUnits = append(abnormalUnits, unit)
		} else { // 没有异常，将unit置为正常，继续
			unit.ValidateState = fuseCommon.ValidateStatePassed
			hitRuleID = 0
			if unit.Type == fuseCommon.TransactionTypeDeposit {
				err = tModel.UpdateDepositTransactionUnitByID(ctx, int64(unit.ID), &unit.ValidateState, &hitRuleID, nil, nil, nil)
			} else {
				err = tModel.UpdateWithdrawTransactionUnitByID(ctx, int64(unit.ID), &unit.ValidateState, &hitRuleID, nil, nil, nil)
			}
			if err != nil {
				return abnormalUnits, err
			}
		}
	}
	return abnormalUnits, nil
}

func validateWithdrawUnit(ctx context.Context, unit *tModel.TransactionUnitTable) (hit *bool, hitRuleID int64, err error) {
	token, err := assetsModel.QueryTokenByTokenID(ctx, unit.TokenID) // 先查询下token是否配置
	if err != nil && !errors.Is(err, ErrRecordNotFound) {            // 出错，报错
		return nil, 0, err
	} else if errors.Is(err, ErrRecordNotFound) { // token没有配置
		GetLogger(ctx).Infof("validate unit, unitID: %d, token %s not found, skip.", unit.ID, unit.TokenID)
		return new(bool), 0, nil
	}
	unitID := int64(unit.ID)
	var latestTimestamp int64
	orderAmount, statementAmount := new(big.Rat), new(big.Rat)
	withdrawOrders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		TransactionUnitIDs: []int64{unitID},
		WithdrawStates:     []fuseCommon.WithdrawOrderState{fuseCommon.WithdrawOrderStateSuccess}, // 只比对提币成功的订单
	})
	if err != nil {
		return nil, 0, err
	}
	for _, order := range withdrawOrders {
		amount := new(big.Rat)
		amountInt, ok := amount.SetString(order.Amount)
		if !ok {
			return nil, 0, fuseCommon.ErrParseAmount(order.Amount)
		}
		if order.State == fuseCommon.WithdrawOrderStateSuccess {
			orderAmount = orderAmount.Add(orderAmount, amountInt)
		}
		if order.UpdateTime.Unix() > latestTimestamp {
			latestTimestamp = order.UpdateTime.Unix()
		}
	}
	statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
		TransactionUnitIDs: []int64{unitID},
		BusinessType:       fuseCommon.StatementBusinessTypeWithdraw,
	})
	if err != nil {
		return nil, 0, err
	}
	for _, statement := range statements {
		// statement的数量需要除以token的小数位数
		amount := new(big.Rat)
		amountInt, ok := amount.SetString(statement.Amount)
		if !ok {
			return nil, 0, fuseCommon.ErrParseAmount(statement.Amount)
		}
		amountFloat := amountInt
		if token.Decimals > 0 {
			divisor := new(big.Rat).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(token.Decimals), nil))
			amountFloat = amountInt.Quo(amountInt, divisor)
		}
		if statement.State == fuseCommon.StatementStateOnchain {
			statementAmount = statementAmount.Add(statementAmount, amountFloat)
		} else if statement.State == fuseCommon.StatementStateRevert { // TODO 确定提现回滚时amount是正还是负
			statementAmount = statementAmount.Add(statementAmount, amountFloat)
		}
		if statement.CreatedTimestamp/1000 > latestTimestamp { // 需要对timestamp转换
			latestTimestamp = statement.CreatedTimestamp / 1000
		}
		// 提现流水的amount是负的，而订单那边的值是正的，因此要处理一下
		statementAmount = statementAmount.Neg(statementAmount)
	}
	amountDiff := new(big.Rat)
	return rule_engine.ExecuteRules(ctx, map[string]interface{}{
		fuseCommon.ParamWalletID:         unit.WalletID,
		fuseCommon.ParamChain:            unit.ChainID,
		fuseCommon.ParamToken:            unit.TokenID,
		fuseCommon.ParamAmountDifference: amountDiff.Sub(statementAmount, orderAmount), // 上链量-上账量
		fuseCommon.ParamLatency:          time.Now().Unix() - latestTimestamp,
		fuseCommon.ParamTransactionType:  fuseCommon.TransactionTypeDeposit,
	})
}

func validateDepositUnit(ctx context.Context, unit *tModel.TransactionUnitTable) (hit *bool, hitRuleID int64, err error) {
	token, err := assetsModel.QueryTokenByTokenID(ctx, unit.TokenID) // 先查询下token是否配置
	if err != nil && !errors.Is(err, ErrRecordNotFound) {            // 出错，报错
		return nil, 0, err
	} else if errors.Is(err, ErrRecordNotFound) { // token没有配置
		GetLogger(ctx).Infof("validate unit, unitID: %d, token %s not found, skip.", unit.ID, unit.TokenID)
		return new(bool), 0, nil
	}
	unitID := int64(unit.ID)
	var latestTimestamp int64
	orderAmount, statementAmount := new(big.Rat), new(big.Rat)
	depositOrders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		TransactionUnitIDs: []int64{unitID},
		DepositStates:      []fuseCommon.DepositOrderState{fuseCommon.DepositOrderStateSuccess}, // 只比对充值成功的订单
	})
	if err != nil {
		return nil, 0, err
	}
	for _, order := range depositOrders {
		amount := new(big.Rat)
		amountInt, ok := amount.SetString(order.Amount)
		if !ok {
			return nil, 0, fuseCommon.ErrParseAmount(order.Amount)
		}
		if order.State == fuseCommon.DepositOrderStateSuccess {
			orderAmount = orderAmount.Add(orderAmount, amountInt)
		}
		if order.UpdateTime.Unix() > latestTimestamp {
			latestTimestamp = order.UpdateTime.Unix()
		}
	}
	statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
		TransactionUnitIDs: []int64{unitID},
		BusinessType:       fuseCommon.StatementBusinessTypeDeposit,
	})
	if err != nil {
		return nil, 0, err
	}
	for _, statement := range statements {
		// statement的数量需要除以token的小数位数
		amount := new(big.Rat)
		amountInt, ok := amount.SetString(statement.Amount)
		if !ok {
			return nil, 0, fuseCommon.ErrParseAmount(statement.Amount)
		}
		amountFloat := amountInt
		if token.Decimals > 0 {
			divisor := new(big.Rat).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(token.Decimals), nil))
			amountFloat = amountInt.Quo(amountInt, divisor)
		}
		if statement.State == fuseCommon.StatementStateOnchain {
			statementAmount = statementAmount.Add(statementAmount, amountFloat)
		} else if statement.State == fuseCommon.StatementStateRevert { // TODO 确定充值回滚时amount是负还是正
			statementAmount = statementAmount.Add(statementAmount, amountFloat)
		}
		if statement.CreatedTimestamp/1000 > latestTimestamp {
			latestTimestamp = statement.CreatedTimestamp / 1000
		}
	}
	amountDiff := new(big.Rat)
	return rule_engine.ExecuteRules(ctx, map[string]interface{}{
		fuseCommon.ParamWalletID:         unit.WalletID,
		fuseCommon.ParamChain:            unit.ChainID,
		fuseCommon.ParamToken:            unit.TokenID,
		fuseCommon.ParamAmountDifference: amountDiff.Sub(orderAmount, statementAmount), // 上账量-上链量
		fuseCommon.ParamLatency:          time.Now().Unix() - latestTimestamp,
		fuseCommon.ParamTransactionType:  fuseCommon.TransactionTypeDeposit,
	})
}
