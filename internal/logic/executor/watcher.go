/**
 * @note
 * watcher
 *
 * <AUTHOR>
 * @date 	2025-04-28
 */
package executor

import (
	// 系统内置包
	"context"
	"time"

	// gitlab.docsl.com的包
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
)

func GetMaxUpdateTimesAndCount(ctx context.Context) (ruleMaxUpdateTime, ruleRelationMaxUpdateTime, tokenMaxUpdateTime, chainMaxUpdateTime time.Time, ruleRelationCount int64, err error) {
	// 获取规则最大更新时间
	ruleMaxUpdateTime, err = ruleModel.QueryRuleMaxUpdateTime(ctx)
	if err != nil {
		return
	}

	// 获取规则关系最大更新时间
	ruleRelationMaxUpdateTime, err = ruleModel.QueryRuleRelationMaxUpdateTime(ctx)
	if err != nil {
		return
	}

	// 获取代币最大更新时间
	tokenMaxUpdateTime, err = assetsModel.QueryTokenMaxUpdateTime(ctx)
	if err != nil {
		return
	}

	// 获取链最大更新时间
	chainMaxUpdateTime, err = assetsModel.QueryChainMaxUpdateTime(ctx)
	if err != nil {
		return
	}

	// 获取规则关系总数
	ruleRelationCount, err = ruleModel.QueryRuleRelationTotalCount(ctx)
	if err != nil {
		return
	}

	return
}
