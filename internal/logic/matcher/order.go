/**
 * @note
 * 以订单为驱动，以订单为准（只看充值订单），创建transaction_unit，再去匹配相应的充值流水
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package matcher

import (
	"context"

	. "gitlab.docsl.com/security/common"
	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func OrderMatch(ctx context.Context) (err error) {
	// 寻找还没有transactionUnit的充值订单
	var ordersToBeMatched []*tModel.DepositOrderTable
	for page := 1; ; page++ {
		depositSuccessOrders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
			Page:           page,
			PerPage:        fuseCommon.MatchOrderBatchSize,
			ValidateStates: []fuseCommon.ValidateState{fuseCommon.ValidateStateToBeValidated}, // 检查没有校验通过的，成功的订单
			DepositStates: []fuseCommon.DepositOrderState{ // 只关心成功的
				fuseCommon.DepositOrderStateSuccess,
			},
			Order: "update_time",
		})
		if err != nil {
			return err
		}
		ordersToBeMatched = append(ordersToBeMatched, depositSuccessOrders...)
		if len(depositSuccessOrders) < fuseCommon.MatchOrderBatchSize {
			break
		}
	}
	for _, order := range ordersToBeMatched {
		_, errCreateUnit := tModel.CreateOrUpdateTransactionUnitByDepositOrder(ctx, order)
		if errCreateUnit != nil {
			GetLogger(ctx).Errorf("deposit order matching, create transaction unit err: %v", errCreateUnit)
			err = errCreateUnit
			continue
		}
	}
	return err
}
