/**
 * @note
 * 以流水为驱动，以流水为准（提现流水、提现回滚再提现流水、和充值回滚流水），创建transaction_unit，再去匹配订单
 * 特别地，如果发现充值回滚流水，需要把原本已对比完成的transaction_unit状态置为未对比状态
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package matcher

import (
	"context"

	. "gitlab.docsl.com/security/common"
	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// StatementMatch 流水处理，调用时需要分别对提现流水和充值回滚流水进行调用
// businessType fuseCommon.StatementBusinessType 流水类型（充值或提现）
// state fuseCommon.StatementState 流水状态（上链或回滚）
func StatementMatch(ctx context.Context, businessType fuseCommon.StatementBusinessType, state *fuseCommon.StatementState) (err error) {
	// 寻找还没有transactionUnit的流水
	var statementsToBeMatched []*tModel.StatementTable
	for page := 1; ; page++ {
		statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
			Page:               page,
			PerPage:            fuseCommon.MatchStatementBatchSize,
			TransactionUnitIDs: []int64{0},
			BusinessType:       businessType,
			State:              state,
			Order:              "created_timestamp",
		})
		if err != nil {
			return err
		}
		statementsToBeMatched = append(statementsToBeMatched, statements...)
		if len(statements) < fuseCommon.MatchStatementBatchSize {
			break
		}
	}

	for _, statement := range statementsToBeMatched {
		// 充值回滚流水，referID一定!= 0，应该顺藤摸瓜找到之前所有的unit并加入，然后将unit的状态改回未validate状态
		rootStatement := statement
		childStatements := make([]*tModel.StatementTable, 0)
		// 这里需要特殊处理，如果是回滚/回滚再提现流水，referID != 0，应该顺藤摸瓜找到之前所有的unit并加入，然后将unit的状态改回未validate状态
		for rootStatement.ReferID != StringEmpty && rootStatement.ReferID != "0" {
			// 向上一直找到rootStatement
			statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
				WalletID:     rootStatement.WalletID, // 2025-06-12 流水ID不是唯一键，要加上walletID和chainID一起看
				ChainID:      rootStatement.ChainID,
				StatementIDs: []string{rootStatement.ReferID},
			})
			if err != nil {
				return err
			}
			if len(statements) > 1 {
				return fuseCommon.ErrRootStatementNotUnique
			} else if len(statements) == 0 {
				return ErrRecordNotFound
			}
			childStatements = append(childStatements, rootStatement)
			rootStatement = statements[0]
		}
		_, errCreateUnit := tModel.CreateOrUpdateTransactionUnitByStatements(ctx, func(statementType fuseCommon.StatementBusinessType) fuseCommon.TransactionType {
			if statementType == fuseCommon.StatementBusinessTypeWithdraw {
				return fuseCommon.TransactionTypeWithdraw
			}
			return fuseCommon.TransactionTypeDeposit
		}(businessType), rootStatement, childStatements...)
		if errCreateUnit != nil {
			GetLogger(ctx).Errorf("statement matching, create transaction unit err: %v", errCreateUnit)
			err = errCreateUnit
			continue
		}
	}
	return err
}
