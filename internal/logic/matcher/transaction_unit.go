/**
 * @note
 * 以Transaction Unit为驱动，两边都尝试去匹配，从而补齐所有信息，保证系统完备性
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package matcher

import (
	"context"
	"errors"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/fuse/internal/model/assets"
	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
	"math/big"
	"slices"
)

func TransactionUnitMatch(ctx context.Context) (err error) {
	// 寻找还没有校验，以及校验失败的transactionUnit的流水
	var unitsToBeMatched []*tModel.TransactionUnitTable
	for page := 1; ; page++ {
		incompleteUnits, err := tModel.QueryTransactionUnitsBySeveralConditions(ctx, tModel.QueryTransactionUnitFilter{
			Page:    page,
			PerPage: fuseCommon.MatchTransactionUnitBatchSize,
			ValidateStates: []fuseCommon.ValidateState{ // 拉取未校验以及校验不通过的unit
				fuseCommon.ValidateStateToBeValidated,
				fuseCommon.ValidateStateAbnormal,
			},
			Order: "created_at",
		})
		if err != nil {
			return err
		}
		unitsToBeMatched = append(unitsToBeMatched, incompleteUnits...)
		if len(incompleteUnits) < fuseCommon.MatchTransactionUnitBatchSize {
			break
		}
	}
	unitIDs := make([]int64, 0)
	for _, incompleteUnit := range unitsToBeMatched {
		unitIDs = append(unitIDs, int64(incompleteUnit.ID))
		switch incompleteUnit.Type {
		case fuseCommon.TransactionTypeDeposit:
			if errProcess := processDepositTransactionUnit(ctx, incompleteUnit); errProcess != nil {
				err = errProcess
				continue
			}
		case fuseCommon.TransactionTypeWithdraw:
			if errProcess := processWithdrawTransactionUnit(ctx, incompleteUnit); errProcess != nil {
				err = errProcess
				continue
			}
		}
	}
	return err
}

// 处理充值交易
func processDepositTransactionUnit(ctx context.Context, unit *tModel.TransactionUnitTable) (err error) {
	unitID := int64(unit.ID)
	tobeValidate := fuseCommon.ValidateStateToBeValidated
	var unitStatementIDs *tModel.StatementIDsArray
	var needUpdateStatementIDs []string
	// 充值交易，补全流水
	statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
		ChainID:         unit.ChainID,
		TokenID:         unit.TokenID,
		TransactionHash: unit.TransactionHash,
		TransferIndex:   &unit.TransferIndex,
		ToAddress:       unit.ToAddress,
		Memo:            unit.Memo,
		BusinessType:    fuseCommon.StatementBusinessTypeDeposit,
	})
	if err != nil {
		return err
	}
	// 统计要更新的unit和statement
	for _, statement := range statements {
		if !slices.Contains(unit.StatementIDs, statement.StatementID) {
			if unitStatementIDs == nil { // 有新的statement了，更新所有流水的validate状态
				unitStatementIDs = new(tModel.StatementIDsArray)
				*unitStatementIDs = append(*unitStatementIDs, unit.StatementIDs...)
			}
			*unitStatementIDs = append(*unitStatementIDs, statement.StatementID)
		}
		if statement.TransactionUnitID != int64(unit.ID) { // 同样，有新的statement了，更新所有流水的validate状态
			needUpdateStatementIDs = append(needUpdateStatementIDs, statement.StatementID)
		}
	}
	// 2025-05-29 兜底充值回滚的情况：1.可能出现多笔充值订单，2.把非成功的充值订单也查出来，好进行匹配和展示
	var unitOrderIDs *tModel.OrderIDsArray
	var needUpdateDepositOrderIDs []int64
	orders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		ChainID:            unit.ChainID,
		TokenID:            unit.TokenID,
		TransactionHash:    unit.TransactionHash,
		TransferIndex:      &unit.TransferIndex,
		ToAddress:          unit.ToAddress,
		Memo:               unit.Memo,
		TransactionUnitIDs: []int64{0}, // 只查还没匹配到unit的订单
	})
	if err != nil {
		return err
	}
	for _, order := range orders {
		if !slices.Contains(unit.OrderIDs, order.OrderID) { // 查出有没匹配的订单了，需要同时更新订单的状态
			if unitOrderIDs == nil { // 有新的order了，更新所有order的validate状态
				unitOrderIDs = new(tModel.OrderIDsArray)
				*unitOrderIDs = append(*unitOrderIDs, unit.OrderIDs...)
			}
			*unitOrderIDs = append(*unitOrderIDs, order.OrderID) // 更新orderIDs
		}
		needUpdateDepositOrderIDs = append(needUpdateDepositOrderIDs, order.OrderID) // 有新的orderID了，更新order的validate状态
	}

	// 执行更新
	if unitStatementIDs != nil || len(needUpdateStatementIDs) > 0 ||
		unitOrderIDs != nil || len(needUpdateDepositOrderIDs) > 0 {
		err = tModel.UpdateTransactionUnitAndStatementsAndOrdersAllAtOnce(ctx, unit.WalletID, unit.ChainID, unitID,
			unitStatementIDs, unitOrderIDs, &tobeValidate, needUpdateStatementIDs,
			needUpdateDepositOrderIDs, nil, &unitID, &tobeValidate)
	}
	return err
}

// 处理提现交易
func processWithdrawTransactionUnit(ctx context.Context, unit *tModel.TransactionUnitTable) (err error) {
	if len(unit.OrderIDs) > 0 { //TODO 2025-05-15 这里由于忽略了index 因此一个unit只能匹配一个order了
		return nil
	}
	unitID := int64(unit.ID)
	tobeValidate := fuseCommon.ValidateStateToBeValidated
	var unitOrderIDs *tModel.OrderIDsArray
	var needUpdateWithdrawOrderIDs []int64
	// 提现交易，补全订单
	// 2025-05-15 这里因为订单那里是没有transferIndex的，因此忽略index全查出来，再使用amount进行匹配
	// 2025-05-23 钱包会将同一个地址的提币拆分到两笔交易里去，因此就不存在index的问题了，可以直接忽略index进行匹配
	statementState := fuseCommon.StatementStateOnchain
	statements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
		BusinessType:       fuseCommon.StatementBusinessTypeWithdraw,
		TransactionUnitIDs: []int64{int64(unit.ID)},
		State:              &statementState,
	})
	if err != nil {
		return err
	}
	if len(statements) == 0 {
		return common.ErrRecordNotFound
	}
	orders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		ChainID:            unit.ChainID,
		TokenID:            unit.TokenID,
		TransactionHash:    unit.TransactionHash,
		FormattedToAddress: unit.ToAddress,
		Memo:               unit.Memo,
		TransactionUnitIDs: []int64{0}, // 只查还没匹配到unit的订单
	})
	if err != nil {
		return err
	}
	if len(orders) == 0 { // 如果没查到，就去掉address的条件再查出来，然后调用钱包接口对地址转换看看对不对得上
		noToAddressOrders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
			ChainID:            unit.ChainID,
			TokenID:            unit.TokenID,
			TransactionHash:    unit.TransactionHash,
			Memo:               unit.Memo,
			TransactionUnitIDs: []int64{0}, // 只查还没匹配到unit的订单
		})
		if err != nil {
			return err
		}
		for _, order := range noToAddressOrders {
			if formattedAddress, err := wallet.FormatAddress(ctx, unit.ChainID, order.ToAddress); err != nil {
				common.GetLogger(ctx).Errorf("order %d format address err: %v", order.OrderID, err)
			} else if !formattedAddress.FormatSuccess {
				common.GetLogger(ctx).Errorf("order %d format address not success: %v", order.OrderID, formattedAddress)
			} else if formattedAddress.FormatAddress == unit.ToAddress { // 匹配了
				orders = append(orders, order)
			}
		}
	}
	// 2025-05-26 订单和提币流水中的to_address有可能是不同格式的，如果第一次找不到，去掉toAddress再找一遍，然后调用钱包的format接口进行地址转换
	// 统计要更新的unit和statement
	// 由于提币订单里拿不到index，有可能存在多对多的情况，为表最严谨的情况，选择所有order里，amount最接近的order认为是匹配的
	var mostMatchedOrder *tModel.WithdrawOrderTable // 一笔提现交易只能有一笔提现订单，出现回滚用户再次提现，是一笔新的，所以这里只认为会有一笔
	var minDiff *big.Rat
	for _, order := range orders {
		if diff, err := diffAmount(ctx, statements[0], order); err != nil {
			return err
		} else if minDiff == nil || diff.Cmp(minDiff) == -1 { // 这个订单的diff更小，所以取这个订单
			minDiff = diff
			mostMatchedOrder = order
		}
	}
	if mostMatchedOrder != nil {
		unitOrderIDs = new(tModel.OrderIDsArray)
		*unitOrderIDs = append(*unitOrderIDs, mostMatchedOrder.OrderID)                           // 更新orderIDs
		needUpdateWithdrawOrderIDs = append(needUpdateWithdrawOrderIDs, mostMatchedOrder.OrderID) // 有新的orderID了，更新order的validate状态
	}
	// 执行更新
	if unitOrderIDs != nil || len(needUpdateWithdrawOrderIDs) > 0 {
		err = tModel.UpdateTransactionUnitAndStatementsAndOrdersAllAtOnce(ctx, unit.WalletID, unit.ChainID, unitID,
			nil, unitOrderIDs, &tobeValidate, nil,
			nil, needUpdateWithdrawOrderIDs, &unitID, &tobeValidate)
	}
	return err
}

func diffAmount(ctx context.Context, statement *tModel.StatementTable, order *tModel.WithdrawOrderTable) (*big.Rat, error) {
	// 1. 解析金额字符串为 big.Rat (有理数，可以精确表示小数)
	orderAmount, ok := new(big.Rat).SetString(order.Amount)
	if !ok {
		return nil, errors.New("parse order amount failed")
	}
	statementAmount, ok := new(big.Rat).SetString(statement.Amount)
	if !ok {
		return nil, errors.New("parse statement amount failed")
	}
	token, err := assets.QueryTokenByTokenID(ctx, order.TokenID)
	if err != nil {
		return nil, err
	}
	if token != nil && token.Decimals > 0 {
		// 2. 计算 10^decimals
		multiplier := new(big.Rat).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(token.Decimals), nil))
		// 3. 乘法: amount * 10^decimals
		orderAmount = new(big.Rat).Mul(orderAmount, multiplier)
	}

	// 4. statement的提现金额是负的，所以要处理一下
	statementAmount = statementAmount.Neg(statementAmount)

	// 5. 与流水金额作差
	diff := orderAmount.Sub(orderAmount, statementAmount)

	// 6. 取绝对值
	return diff.Abs(diff), nil
}
