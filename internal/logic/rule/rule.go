/**
 * @note
 * rule
 *
 * <AUTHOR>
 * @date 	2025-04-21
 */
package rule

import (
	"context"

	set "github.com/deckarep/golang-set/v2"

	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func QueryRuleList(ctx context.Context, page, perPage int, chainID, tokenID string, ruleStatus *fuseCommon.RuleStatus) (
	items []*RuleItem, count int64, err error) {
	// 先查询规则总数
	filter := ruleModel.QueryFuseRuleFilter{
		Page:    page,
		PerPage: perPage,
		Status:  ruleStatus,
	}

	// 处理chainID或tokenID过滤条件
	var assetIDs []int64
	var assetType fuseCommon.AssetType

	if chainID != common.StringEmpty {
		// 如果指定了chainID，查询该chainID对应的链
		chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
			ChainIDs: []string{chainID},
		})
		if err != nil {
			return nil, 0, err
		}

		// 收集chainID对应的资产ID
		for _, chain := range chains {
			assetIDs = append(assetIDs, int64(chain.ID))
		}
		assetType = fuseCommon.AssetTypeChain
	} else if tokenID != common.StringEmpty {
		// 如果指定了tokenID，查询该tokenID对应的token
		tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
			TokenIDs: []string{tokenID},
		})
		if err != nil {
			return nil, 0, err
		}

		// 收集tokenID对应的资产ID
		for _, token := range tokens {
			assetIDs = append(assetIDs, int64(token.ID))
		}
		assetType = fuseCommon.AssetTypeToken
	}

	// 如果指定了chainID或tokenID，先查询资产和规则的绑定关系
	if len(assetIDs) > 0 {
		relations, err := ruleModel.QueryFuseRuleRelationBySeveralConditions(ctx, ruleModel.QueryFuseRuleRelationFilter{
			AssetType: assetType,
			AssetIDs:  assetIDs,
		})
		if err != nil {
			return nil, 0, err
		}

		// 收集所有关联的ruleID
		ruleIDSet := set.NewSet[int64]()
		for _, relation := range relations {
			ruleIDSet.Add(relation.RuleID)
		}

		if ruleIDSet.Cardinality() > 0 {
			filter.RuleIDs = ruleIDSet.ToSlice()
		} else {
			// 如果没有找到任何规则关联，返回空结果
			return []*RuleItem{}, 0, nil
		}
	}

	// 查询规则列表
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 查询规则总数
	count, err = ruleModel.QueryFuseRuleCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 转换为返回格式
	items = make([]*RuleItem, 0, len(rules))
	for _, rule := range rules {
		items = append(items, &RuleItem{
			RuleID:     int64(rule.ID),
			RuleName:   rule.Name,
			Desc:       rule.Desc,
			Priority:   rule.Priority,
			RuleStatus: rule.Status,
		})
	}

	return items, count, nil
}

func QueryRuleDetail(ctx context.Context, ruleID int64) (item *RuleDetailItem, err error) {
	// 查询规则详情
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs: []int64{ruleID},
	})
	if err != nil {
		return nil, err
	}

	if len(rules) == 0 {
		return nil, fuseCommon.ErrRuleIDsDoNotExist([]int64{ruleID})
	}

	rule := rules[0]

	// 转换为返回格式
	item = &RuleDetailItem{
		RuleItem: RuleItem{
			RuleID:     int64(rule.ID),
			RuleName:   rule.Name,
			Desc:       rule.Desc,
			Priority:   rule.Priority,
			RuleStatus: rule.Status,
		},
		RuleDetail: rule.Detail,
		Actions:    rule.Actions,
	}

	return item, nil
}

func CreateRule(ctx context.Context, name, desc string, priority int64, status fuseCommon.RuleStatus,
	detail *ruleModel.RuleDetail, actions *ruleModel.ActionDetail) (item *RuleItem, err error) {

	// 创建规则
	ruleID, err := ruleModel.CreateFuseRule(ctx, name, desc, priority, detail, actions)
	if err != nil {
		return nil, err
	}

	// 如果状态不是默认的禁用状态，需要更新状态
	if status != fuseCommon.RuleStatusDisabled {
		err = ruleModel.UpdateFuseRuleByRuleID(ctx, ruleID, nil, nil, nil, &status, nil, nil)
		if err != nil {
			return nil, err
		}
	}

	// 查询创建的规则
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs: []int64{ruleID},
	})
	if err != nil {
		return nil, err
	}

	if len(rules) == 0 {
		return nil, fuseCommon.ErrRuleIDsDoNotExist([]int64{ruleID})
	}

	rule := rules[0]

	// 转换为返回格式
	item = &RuleItem{
		RuleID:     int64(rule.ID),
		RuleName:   rule.Name,
		Desc:       rule.Desc,
		Priority:   rule.Priority,
		RuleStatus: rule.Status,
	}

	return item, nil
}

func ModifyRules(ctx context.Context, inputs []*ModifyRuleInput) (err error) {
	// 收集所有规则ID
	ruleIDs := make([]int64, 0, len(inputs))
	for _, input := range inputs {
		ruleIDs = append(ruleIDs, input.RuleID)
	}

	// 验证所有规则是否存在
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs: ruleIDs,
	})
	if err != nil {
		return err
	}

	// 检查是否所有规则都存在
	if len(rules) < len(ruleIDs) {
		// 找出不存在的规则ID
		existRuleIDSet := set.NewSet[int64]()
		for _, rule := range rules {
			existRuleIDSet.Add(int64(rule.ID))
		}

		var missingRuleIDs []int64
		for _, ruleID := range ruleIDs {
			if !existRuleIDSet.Contains(ruleID) {
				missingRuleIDs = append(missingRuleIDs, ruleID)
			}
		}

		return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
	}

	// 逐个处理规则更新
	for _, input := range inputs {
		// 验证规则详情
		if input.RuleDetail != nil {
			for _, subRule := range input.RuleDetail.SubRules {
				if err := subRule.Validate(); err != nil {
					return err
				}
			}
		}

		// 更新规则
		err = ruleModel.UpdateFuseRuleByRuleID(ctx, input.RuleID, input.RuleName, input.Desc, input.Priority,
			input.RuleStatus, input.RuleDetail, input.Actions)
		if err != nil {
			return err
		}
	}

	return nil
}

func DeleteRules(ctx context.Context, ruleIDs []int64) (err error) {
	// 验证规则是否存在
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		RuleIDs: ruleIDs,
	})
	if err != nil {
		return err
	}

	// 检查是否所有规则都存在
	if len(rules) < len(ruleIDs) {
		// 找出不存在的规则ID
		existRuleIDSet := set.NewSet[int64]()
		for _, rule := range rules {
			existRuleIDSet.Add(int64(rule.ID))
		}

		var missingRuleIDs []int64
		for _, ruleID := range ruleIDs {
			if !existRuleIDSet.Contains(ruleID) {
				missingRuleIDs = append(missingRuleIDs, ruleID)
			}
		}

		return fuseCommon.ErrRuleIDsDoNotExist(missingRuleIDs)
	}

	// 删除规则
	if err = ruleModel.DeleteFuseRule(ctx, ruleIDs); err != nil {
		return err
	}

	// 再清理关联关系
	_, err = ruleModel.DeleteFuseRuleRelationsByRuleIDs(ctx, ruleIDs)
	return err

}
