/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-04-21
 */
package rule

import (
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type RuleItem struct {
	RuleID     int64                 `json:"ruleID,string"`
	RuleName   string                `json:"ruleName"`
	Desc       string                `json:"desc"`
	Priority   int64                 `json:"priority"`
	RuleStatus fuseCommon.RuleStatus `json:"ruleStatus"`
}

type RuleDetailItem struct {
	RuleItem
	RuleDetail *ruleModel.RuleDetail   `json:"ruleDetail"`
	Actions    *ruleModel.ActionDetail `json:"actions"` // 规则内容
}

type ModifyRuleInput struct {
	RuleID     int64                   `json:"ruleID,string"`
	RuleName   *string                 `json:"ruleName"`
	Desc       *string                 `json:"desc"`
	Priority   *int64                  `json:"priority"`
	RuleStatus *fuseCommon.RuleStatus  `json:"ruleStatus"`
	RuleDetail *ruleModel.RuleDetail   `json:"ruleDetail"`
	Actions    *ruleModel.ActionDetail `json:"actions"`
}
