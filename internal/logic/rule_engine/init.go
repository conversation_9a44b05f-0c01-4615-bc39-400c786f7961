/**
 * @note
 * engine
 *
 * <AUTHOR>
 * @date 	2025-03-28
 */
package rule_engine

import (
	"context"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"

	"github.com/bilibili/gengine/engine"

	. "gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

var (
	ep      *engine.GenginePool
	enabled atomic.Bool
	mutex   sync.RWMutex
)

func getEnginePool() *engine.GenginePool {
	mutex.RLock()
	defer mutex.RUnlock()
	// 这里返回指针，后续修改原指针指向的对象，不会影响被复制出来的指针 所指向的对象，故没有并发安全问题
	ret := ep
	return ret
}

// InitEngine 读取原始rules并编译
func InitEngine(ctx context.Context) error {
	ruleStatus := fuseCommon.RuleStatusEnabled // 只查enable的
	rules, err := ruleModel.QueryFuseRuleBySeveralConditions(ctx, ruleModel.QueryFuseRuleFilter{
		Status: &ruleStatus,
	})
	if err != nil {
		return err
	}
	ruleIDs := make([]int64, 0)
	for _, rule := range rules {
		ruleIDs = append(ruleIDs, int64(rule.ID))
	}
	// 查询绑定关系
	relations, err := ruleModel.QueryFuseRuleRelationBySeveralConditions(ctx, ruleModel.QueryFuseRuleRelationFilter{
		RuleIDs: ruleIDs,
	})
	if err != nil {
		return err
	}
	// 数据整理进map方便使用
	chainIDs := make([]int64, 0)
	tokenIDs := make([]int64, 0)
	ruleIDRelationMap := make(map[int64][]*ruleModel.RuleRelationTable)
	for _, relation := range relations {
		ruleIDRelationMap[relation.RuleID] = append(ruleIDRelationMap[relation.RuleID], relation)
		switch relation.AssetType {
		case fuseCommon.AssetTypeToken:
			tokenIDs = append(tokenIDs, relation.AssetID)
		case fuseCommon.AssetTypeChain:
			chainIDs = append(chainIDs, relation.AssetID)
		}
	}
	chainIDMap := make(map[int64]*assetsModel.ChainTable)
	tokenIDMap := make(map[int64]*assetsModel.TokenTable)
	// 查出规则所关联的chain和token信息
	if len(chainIDs) > 0 {
		chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
			IDs: chainIDs,
		})
		if err != nil {
			return err
		}
		for _, chain := range chains {
			chainIDMap[int64(chain.ID)] = chain
		}
	}
	if len(tokenIDs) > 0 {
		tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
			IDs: tokenIDs,
		})
		if err != nil {
			return err
		}
		for _, token := range tokens {
			tokenIDMap[int64(token.ID)] = token
		}
	}
	// 拼装成rules
	processedRules := make([]*FuseRule, 0)
	for _, rule := range rules {
		processedRule := &FuseRule{
			ID:       int64(rule.ID),
			Name:     rule.Name,
			Desc:     rule.Desc,
			Priority: rule.Priority,
			ChainMap: make(ChainMap),
			TokenMap: make(TokenMap),
			SubRules: rule.Detail.SubRules,
		}
		// 根据rule取relation
		relationsOfRule, ok := ruleIDRelationMap[int64(rule.ID)]
		if !ok {
			continue
		}
		for _, relation := range relationsOfRule {
			switch relation.AssetType { // 组装rule中的chainMap和tokenMap
			case fuseCommon.AssetTypeToken:
				token, ok := tokenIDMap[relation.AssetID]
				if !ok {
					continue
				}
				if processedRule.TokenMap[token.TokenID] == nil {
					processedRule.TokenMap[token.TokenID] = make(map[string]bool)
				}
				processedRule.TokenMap[token.TokenID][token.ChainID] = true
			case fuseCommon.AssetTypeChain:
				if chain, ok := chainIDMap[relation.AssetID]; ok {
					processedRule.ChainMap[chain.ChainID] = true
				}
			}
		}
		// 有实际绑定资产的rule才执行编译
		if len(processedRule.ChainMap) > 0 || len(processedRule.TokenMap) > 0 {
			processedRules = append(processedRules, processedRule)
		}
	}
	return initEngineByFuseRules(ctx, processedRules)
}

// initEngineByFuseRules 根据规则，重新生成enginePool，替换原有的
func initEngineByFuseRules(ctx context.Context, rules []*FuseRule) error {
	if len(rules) == 0 { // 没有激活的rule，无需初始化
		enabled.Store(false)
		return nil
	}
	valueIdx := int64(0)
	valueMap := make(map[string]interface{})
	allRuleStrs := strings.Builder{}
	// 翻译成规则字符串
	for _, rule := range rules {
		ruleStr, err := rule.Transfer(ctx, &valueIdx, valueMap)
		if err != nil {
			return err
		}
		allRuleStrs.WriteString(ruleStr)
	}
	GetLogger(ctx).Infoln("transfer rules to string:", allRuleStrs.String())
	// 添加udf
	loadUDF(valueMap)
	newEP, err := engine.NewGenginePool(fuseCommon.PoolMinLen, fuseCommon.PoolMaxLen, engine.SortModel, allRuleStrs.String(), valueMap)
	if err != nil {
		return err
	}
	mutex.Lock()
	defer mutex.Unlock()
	ep = newEP
	enabled.Store(true)
	return nil
}

func ExecuteRules(ctx context.Context, params map[string]interface{}) (hit *bool, hitRuleID int64, err error) {
	if !enabled.Load() {
		return new(bool), 0, nil
	}
	enginePool := getEnginePool()
	if enginePool == nil {
		return hit, 0, fuseCommon.ErrGenginePoolNotInitialized
	}
	if params == nil {
		params = make(map[string]interface{})
	}
	params[fuseCommon.ParamStag] = &engine.Stag{StopTag: false}
	err, res := ep.Execute(params, true)
	if err != nil {
		GetLogger(ctx).Errorf("execute rules failed, %s", err.Error())
	}
	var incomplete bool
	for k, v := range res {
		if IsNil(v) {
			incomplete = true
			continue
		}
		if res, ok := v.(bool); ok && res {
			hit = &res
			hitRuleID, err := strconv.ParseInt(k, 10, 64)
			return hit, hitRuleID, err
		}
	}
	if incomplete {
		return nil, 0, err
	}
	return new(bool), 0, err
}
