/**
 * @note
 * kms_test
 *
 * <AUTHOR>
 * @date 	2025-02-13
 */
package rule_engine

import (
	"context"
	"testing"

	"github.com/bilibili/gengine/engine"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/tmsong/hlog"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

var (
	log *hlog.Logger
	ctx context.Context
)

func init() {
	log = hlog.NewLoggerWithConfig(logger.GetHlogConfig(), 0)
	ctx = context.WithValue(context.Background(), common.KeyLogger, log)
}

func Test_ExecuteRule(t *testing.T) {
	fuseRule := &FuseRule{
		ID:       1,
		Name:     "fuseRule",
		Desc:     "test fuse rule",
		Priority: 1,
		TokenMap: map[string]map[string]bool{"USDT": {"ERC20": true, "TRC20": true}},
		ChainMap: map[string]bool{"ERC20": true},
		SubRules: []*ruleModel.SubRule{
			{
				Conditions: []*ruleModel.Condition{
					{
						Param: fuseCommon.ParamLatency,
						Op:    ">",
						Value: 20,
					},
					{
						Param: fuseCommon.ParamAmountDifference,
						Op:    ">",
						Value: 0.1,
					},
					{
						Param: fuseCommon.ParamTransactionType,
						Op:    fuseCommon.OpIn,
						Value: []interface{}{"Deposit", "Withdraw"},
					},
				},
			},
			{
				Conditions: []*ruleModel.Condition{
					{
						Param: fuseCommon.ParamLatency,
						Op:    ">",
						Value: 50,
					},
					{
						Param: fuseCommon.ParamAmountDifference,
						Op:    ">",
						Value: 0.01,
					},
					{
						Param: fuseCommon.ParamTransactionType,
						Op:    fuseCommon.OpNotIn,
						Value: []interface{}{"Deposit", "Withdraw"},
					},
				},
			},
		},
	}

	PatchConvey("Test_ExecuteRule", t, func() {
		PatchConvey("case1 listKeys", func() {
			valueIdx := int64(0)
			valueMap := make(map[string]interface{})
			loadUDF(valueMap)
			ruleStr, err := fuseRule.Transfer(ctx, &valueIdx, valueMap)
			So(err, ShouldBeNil)
			p, err := engine.NewGenginePool(1, 10, engine.SortModel, ruleStr, valueMap)
			So(err, ShouldBeNil)
			stag := &engine.Stag{StopTag: false}
			err, res := p.Execute(map[string]interface{}{
				fuseCommon.ParamChain: "ERC20", fuseCommon.ParamToken: "USDT2", fuseCommon.ParamAmountDifference: 0.001, fuseCommon.ParamLatency: 120, fuseCommon.ParamTransactionType: "test", fuseCommon.ParamStag: stag}, true)
			So(err, ShouldBeNil)
			So(res["1"], ShouldBeFalse)
			err, res = p.Execute(map[string]interface{}{
				fuseCommon.ParamChain: "ERC2", fuseCommon.ParamToken: "USDT", fuseCommon.ParamAmountDifference: 1, fuseCommon.ParamLatency: 120, fuseCommon.ParamTransactionType: "Withdraw", fuseCommon.ParamStag: stag}, true)
			So(err, ShouldBeNil)
			So(res["1"], ShouldBeFalse)
			err, res = p.Execute(map[string]interface{}{
				fuseCommon.ParamChain: "ERC20", fuseCommon.ParamToken: "USDT", fuseCommon.ParamAmountDifference: 1, fuseCommon.ParamLatency: 120, fuseCommon.ParamTransactionType: "Withdraw", fuseCommon.ParamStag: stag}, true)
			So(err, ShouldBeNil)
			So(res["1"], ShouldBeTrue)
		})
	})
}
