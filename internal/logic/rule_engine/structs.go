/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-03-25
 */
package rule_engine

import (
	ruleModel "gitlab.docsl.com/security/fuse/internal/model/rule"
)

type FuseRule struct {
	ID       int64                `json:"id"`
	Name     string               `json:"name"`
	Desc     string               `json:"desc"`
	Priority int64                `json:"priority"`
	TokenMap TokenMap             `json:"tokenMap"` // 对哪些币+链生效,key为token
	ChainMap ChainMap             `json:"chainMap"` // 对哪些链生效
	SubRules []*ruleModel.SubRule `json:"subRules"`
}

type TokenMap map[string]map[string]bool
type ChainMap map[string]bool

func (tm TokenMap) Hit(token, chain string) bool {
	if tm == nil || len(tm) == 0 {
		return false
	}
	if tm[token] == nil {
		return false
	}
	return tm[token][chain]
}

func (cm ChainMap) Hit(chain string) bool {
	if cm == nil || len(cm) == 0 {
		return false
	}
	return cm[chain]
}
