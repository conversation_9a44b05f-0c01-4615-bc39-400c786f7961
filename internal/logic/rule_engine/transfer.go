/**
 * @note
 * compile
 *
 * <AUTHOR>
 * @date 	2025-03-27
 */
package rule_engine

import (
	"context"
	"fmt"
	"strings"

	. "gitlab.docsl.com/security/common"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (r *FuseRule) Transfer(ctx context.Context, valueIdx *int64, valueMap map[string]interface{}) (ruleStr string, err error) {
	if len(r.TokenMap)+len(r.ChainMap) == 0 { // 没有生效token和chain，直接忽略此条规则
		return StringEmpty, nil
	}

	var subRuleStrs, skipSubRuleStrs []string
	for _, subRule := range r.SubRules {
		subRuleStr, skipSubRuleStr, err := subRule.Transfer(ctx, valueIdx, valueMap)
		if err != nil {
			return StringEmpty, err
		}
		if subRuleStr != StringEmpty {
			subRuleStrs = append(subRuleStrs, subRuleStr)
		}
		if skipSubRuleStr != StringEmpty {
			skipSubRuleStrs = append(skipSubRuleStrs, skipSubRuleStr)
		}
	}
	var trueStatement, skipStatement string
	if len(subRuleStrs) == 0 {
		return StringEmpty, nil
	} else if len(subRuleStrs) == 1 {
		trueStatement = subRuleStrs[0]
	} else {
		trueStatement = "(" + strings.Join(subRuleStrs, ") || (") + ")"
	}

	if len(skipSubRuleStrs) == 0 {
		skipStatement = StringEmpty
	} else if len(skipSubRuleStrs) == 1 {
		skipStatement = skipSubRuleStrs[0]
	} else {
		skipStatement = "(" + strings.Join(skipSubRuleStrs, ") || (") + ")"
	}

	tokenMapName := fmt.Sprintf("tokenMap_%d", r.ID)
	chainMapName := fmt.Sprintf("chainMap_%d", r.ID)
	valueMap[tokenMapName] = r.TokenMap
	valueMap[chainMapName] = r.ChainMap

	first := fmt.Sprintf(`rule "%d" "%s:%s" salience %d`, r.ID, r.Name, r.Desc, r.Priority)
	trigger := fmt.Sprintf(`if (!%s.Hit(%s,%s) && !%s.Hit(%s)) {return false}`,
		tokenMapName, fuseCommon.ParamToken, fuseCommon.ParamChain,
		chainMapName, fuseCommon.ParamChain)

	if skipStatement == StringEmpty {
		return fmt.Sprintf(`%s
begin
	%s
	if (%s){
		stag.StopTag = true 
		return true
	}
	return false
end`, first, trigger, trueStatement), nil
	}
	return fmt.Sprintf(`%s
begin
	%s
	if (%s){
		stag.StopTag = true 
		return true
	}
	if (%s){
		return
	}
	return false
end`, first, trigger, trueStatement, skipStatement), nil
}
