/**
 * @note
 * udf
 *
 * <AUTHOR>
 * @date 	2025-03-28
 */
package rule_engine

import (
	"math/big"
	"reflect"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func in(v interface{}, array interface{}) bool {
	switch array.(type) {
	case []interface{}:
		ifaceArray := array.([]interface{})
		for _, item := range ifaceArray {
			if reflect.DeepEqual(item, v) {
				return true
			}
		}
	}
	return false
}

func bigFloatCmp(bf *big.Rat, op, value string) bool {
	valueInt := new(big.Rat)
	_, ok := valueInt.SetString(value)
	if !ok {
		return false
	}
	switch op {
	case ">":
		return bf.Cmp(valueInt) == 1
	case "==":
		return bf.Cmp(valueInt) == 0
	case "<":
		return bf.Cmp(valueInt) == -1
	}
	return false
}

func loadUDF(valueMap map[string]interface{}) {
	valueMap[fuseCommon.OpIn] = in
	valueMap["bigFloatCmp"] = bigFloatCmp
}
