/**
 * @note
 * chain
 *
 * <AUTHOR>
 * @date 	2025-04-25
 */
package syncer

import (
	"context"

	"gitlab.docsl.com/security/common"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
)

// SyncChains 同步链信息
// 从wallet服务获取所有支持的链信息，并将新的链信息写入数据库
// 对于已经存在的链，不会进行更新
func SyncChains(ctx context.Context) error {
	// 调用wallet服务获取所有支持的链信息
	chains, err := wallet.GetSupportedChains(ctx)
	if err != nil {
		return err
	}

	// 如果没有链，直接返回
	if len(chains) == 0 {
		return nil
	}

	// 收集所有链的chainID
	chainIDs := make([]string, 0, len(chains))
	for _, chain := range chains {
		chainIDs = append(chainIDs, chain.ChainID)
	}

	// 查询数据库中已经存在的链
	existingChains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{
		ChainIDs: chainIDs,
	})
	if err != nil {
		return err
	}

	// 创建已存在链的映射，用于快速查找
	existingChainMap := make(map[string]bool)
	for _, chain := range existingChains {
		existingChainMap[chain.ChainID] = true
	}

	// 筛选出需要新增的链
	newChains := make([]*assetsModel.ChainTable, 0)
	for _, chain := range chains {
		// 如果链已经存在，跳过
		if existingChainMap[chain.ChainID] {
			continue
		}

		// 创建新的链记录
		newChain := &assetsModel.ChainTable{
			ChainID:    chain.ChainID,
			ChainName:  chain.Symbol,                // 使用Symbol作为链名称
			FuseType:   fuseCommon.FuseTypeManual,   // 默认为手动熔断
			FuseStatus: fuseCommon.FuseStatusNormal, // 默认为正常状态
		}

		newChains = append(newChains, newChain)
	}

	// 如果没有新增的链，直接返回
	if len(newChains) == 0 {
		return nil
	}

	// 将新增的链写入数据库
	_, err = assetsModel.CreateChains(ctx, newChains)
	if err != nil {
		return err
	}

	return nil
}

// SyncTokens 同步代币信息
// 从wallet服务获取所有支持的代币信息，并将新的代币信息写入数据库
// 对于已经存在的代币，不会进行更新
func SyncTokens(ctx context.Context) error {
	// 首先获取所有链
	chains, err := assetsModel.QueryChainBySeveralConditions(ctx, assetsModel.QueryChainFilter{})
	if err != nil {
		return err
	}

	// 如果没有链，直接返回
	if len(chains) == 0 {
		return nil
	}

	// 对每个链获取其支持的代币
	var totalNewTokens int
	for _, chain := range chains {
		// 调用wallet服务获取链上支持的所有代币
		tokens, err := wallet.GetSupportedTokens(ctx, chain.ChainID)
		if err != nil {
			// 继续处理下一个链，不返回错误
			continue
		}

		// 如果没有代币，继续处理下一个链
		if len(tokens) == 0 {
			continue
		}

		// 收集所有代币的tokenID
		tokenIDs := make([]string, 0, len(tokens))
		for _, token := range tokens {
			tokenIDs = append(tokenIDs, token.TokenID)
		}

		// 查询数据库中已经存在的代币
		existingTokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
			ChainIDs: []string{chain.ChainID},
		})
		if err != nil {
			common.GetLogger(ctx).Errorf("SyncTokens: QueryTokenBySeveralConditions failed for chain %s: %v", chain.ChainID, err)
			// 继续处理下一个链，不返回错误
			continue
		}

		// 创建已存在代币的映射，用于快速查找
		existingTokenMap := make(map[string]bool)
		for _, token := range existingTokens {
			existingTokenMap[token.TokenID] = true
		}

		// 筛选出需要新增的代币
		newTokens := make([]*assetsModel.TokenTable, 0)
		for _, token := range tokens {
			// 如果代币已经存在，跳过
			if existingTokenMap[token.TokenID] {
				continue
			}

			// 创建新的代币记录
			newToken := &assetsModel.TokenTable{
				TokenID:    token.TokenID,
				ChainID:    token.ChainID,
				TokenName:  token.Name,
				Decimals:   token.Decimals,
				FuseType:   fuseCommon.FuseTypeManual,   // 默认为手动熔断
				FuseStatus: fuseCommon.FuseStatusNormal, // 默认为正常状态
			}

			newTokens = append(newTokens, newToken)
		}

		// 如果没有新增的代币，继续处理下一个链
		if len(newTokens) == 0 {
			continue
		}

		// 将新增的代币写入数据库
		_, err = assetsModel.CreateTokens(ctx, newTokens)
		if err != nil {
			// 继续处理下一个链，不返回错误
			continue
		}

		totalNewTokens += len(newTokens)
	}

	return nil
}
