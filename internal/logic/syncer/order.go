/**
 * @note
 * 负责同步order
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package syncer

import (
	"context"
	"gitlab.docsl.com/security/common"
	"strconv"
	"time"

	set "github.com/deckarep/golang-set/v2"

	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/order"
)

func SyncDepositOrderOnce(ctx context.Context, host string, chainIDs []string, startTimeMilli, endTimeMilli int64) (totalSynced, latestTimeMilli int64, err error) {
	chainIDSet := set.NewSet[string](chainIDs...)

	for pageNum := int64(1); ; pageNum++ {
		// 调用API获取充值订单列表
		orders, err := order.GetDepositOrderList(ctx, host, pageNum, fuseCommon.SyncOrderBatchSize, startTimeMilli, endTimeMilli)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		// 如果没有订单，说明已经拉取完毕，退出循环
		if len(orders) == 0 {
			break
		}

		// 将API返回的订单转换为数据库模型
		dbOrders := make([]*tModel.DepositOrderTable, 0, len(orders))
		for _, item := range orders {
			// 过滤不需要的链
			if !chainIDSet.Contains(item.ChainID) {
				continue
			}
			// 创建数据库模型
			dbOrder := &tModel.DepositOrderTable{
				OrderID:          item.ID,
				TenantID:         item.TenantID,
				ClientID:         item.ClientID,
				PortfolioID:      item.PortfolioID,
				WalletOrderID:    item.TransactionID,
				WalletID:         item.WalletID,
				ChainID:          item.ChainID,
				TokenID:          item.TokenID,
				Currency:         item.Currency,
				ToAddressID:      item.ToAddressID,
				ToAddress:        item.ToAddress,
				Memo:             item.Memo,
				TransactionHash:  item.TransactionHash,
				Amount:           item.Amount,
				RiskAddressScore: item.RiskAddressScore,
				RiskHashScore:    item.RiskHashScore,
				StartTime:        item.StartTime,
				EndTime:          item.EndTime,
				State:            fuseCommon.DepositOrderState(item.State),
				Creator:          item.Creator,
				Updater:          item.Updater,
				ValidateState:    fuseCommon.ValidateStateToBeValidated, // 初始状态为待校验
				CreateTime:       time.Time(item.CreateTime),
				UpdateTime:       time.Time(item.UpdateTime),
			}

			// 解析交易序号
			dbOrder.TransferIndex, err = strconv.ParseInt(item.TransactionIndex, 10, 64)
			if err != nil {
				return totalSynced, latestTimeMilli, err
			}

			if latestTimeMilli < dbOrder.UpdateTime.UnixMilli() {
				latestTimeMilli = dbOrder.UpdateTime.UnixMilli()
			}

			if dbOrder.TransferIndex < 0 { // 2025-04-24 Relo lin：index小于0的，这些是我们测试mock的数据哈。
				continue
			}

			dbOrders = append(dbOrders, dbOrder)
		}

		// 将订单保存到数据库，支持插入或更新
		err = tModel.CreateOrUpdateDepositOrders(ctx, dbOrders)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		totalSynced += int64(len(dbOrders))

		// 如果返回的数据量小于页大小，说明已经拉取完毕
		if len(orders) < fuseCommon.SyncOrderBatchSize {
			break
		}
	}

	return totalSynced, latestTimeMilli, nil
}

func SyncWithdrawOrderOnce(ctx context.Context, host string, chainIDs []string, startTimeMilli, endTimeMilli int64) (totalSynced, latestTimeMilli int64, err error) {
	chainIDSet := set.NewSet[string](chainIDs...)

	for pageNum := int64(1); ; pageNum++ {
		// 调用API获取提现订单列表
		orders, err := order.GetWithdrawOrderList(ctx, host, pageNum, fuseCommon.SyncOrderBatchSize, startTimeMilli, endTimeMilli)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		// 如果没有订单，说明已经拉取完毕，退出循环
		if len(orders) == 0 {
			break
		}

		// 将API返回的订单转换为数据库模型
		dbOrders := make([]*tModel.WithdrawOrderTable, 0, len(orders))
		for _, item := range orders {
			// 过滤不需要的链
			if !chainIDSet.Contains(item.ChainID) {
				continue
			}
			if item.ToFormattedAddress == common.StringEmpty { // 兜底，如果没反悔toFormattedAddress，则自己加上
				item.ToFormattedAddress = item.ToAddress
			}
			// 创建数据库模型
			dbOrder := &tModel.WithdrawOrderTable{
				OrderID:            item.ID,
				TenantID:           item.TenantID,
				ClientID:           item.ClientID,
				UserID:             item.UserID,
				PortfolioID:        item.PortfolioID,
				WalletOrderID:      item.WalletOrderID,
				WalletID:           item.WalletID,
				ChainID:            item.ChainID,
				TokenID:            item.TokenID,
				Currency:           item.Currency,
				ToAddressID:        item.ToAddressID,
				ToAddress:          item.ToAddress,
				FormattedToAddress: item.ToFormattedAddress, // 2025-05-26 Relo.lin：PX侧新加参数
				Memo:               item.Memo,
				TransactionHash:    item.TransactionHash,
				Amount:             item.Amount,
				Fee:                item.Fee,
				FeeCurrency:        item.FeeCurrency,
				TotalAmount:        item.TotalAmount,
				RiskAddressScore:   item.RiskAddressScore,
				StartTime:          item.StartTime,
				EndTime:            item.EndTime,
				State:              fuseCommon.WithdrawOrderState(item.State),
				Creator:            item.Creator,
				Updater:            item.Updater,
				ValidateState:      fuseCommon.ValidateStateToBeValidated, // 初始状态为待校验
				CreateTime:         time.Time(item.CreateTime),
				UpdateTime:         time.Time(item.UpdateTime),
			}

			dbOrder.TransferIndex, err = strconv.ParseInt(item.TransactionIndex, 10, 64)
			if err != nil {
				return totalSynced, latestTimeMilli, err
			}

			if latestTimeMilli < dbOrder.UpdateTime.UnixMilli() {
				latestTimeMilli = dbOrder.UpdateTime.UnixMilli()
			}

			if dbOrder.TransferIndex < 0 { // 2025-04-24 Relo lin：index小于0的，这些是我们测试mock的数据哈。
				continue
			}

			dbOrders = append(dbOrders, dbOrder)
		}

		// 将订单保存到数据库，支持插入或更新
		err = tModel.CreateOrUpdateWithdrawOrders(ctx, dbOrders)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		totalSynced += int64(len(dbOrders))

		// 如果返回的数据量小于页大小，说明已经拉取完毕
		if len(orders) < fuseCommon.SyncOrderBatchSize {
			break
		}
	}

	return totalSynced, latestTimeMilli, nil
}
