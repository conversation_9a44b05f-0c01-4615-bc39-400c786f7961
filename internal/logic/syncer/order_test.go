/**
 * @note
 * order_test
 *
 * <AUTHOR>
 * @date 	2025-04-23
 */
package syncer

import (
	"testing"
	"time"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"

	oHelper "gitlab.docsl.com/security/fuse/pkg/helper/order"
)

func Test_GetOrderList(t *testing.T) {
	PatchConvey("Test_GetOrderList", t, func() {
		PatchConvey("case1 GetDepositOrderList", func() {
			startTime := time.Now().Add(-time.Hour * 168).UnixMilli()
			endTime := time.Now().UnixMilli()
			depositOrders, err := oHelper.GetDepositOrderList(ctx, "http://atlas-platform-dw.test-3.common-test.yorkapp.com/", 1, 100, startTime, endTime)
			So(err, ShouldBeNil)
			So(depositOrders, ShouldNotBeEmpty)
			for _, order := range depositOrders {
				So(order.ChainID, ShouldNotBeEmpty)
				So(order.TokenID, ShouldNotBeEmpty)
				So(order.Amount, ShouldNotBeEmpty)
			}
		})
		PatchConvey("case2 GetWithdrawOrderList", func() {
			startTime := time.Now().Add(-time.Hour * 168).UnixMilli()
			endTime := time.Now().UnixMilli()
			withdrawOrders, err := oHelper.GetWithdrawOrderList(ctx, "http://atlas-platform-dw.test-3.common-test.yorkapp.com/", 1, 100, startTime, endTime)
			So(err, ShouldBeNil)
			So(withdrawOrders, ShouldNotBeEmpty)
			for _, order := range withdrawOrders {
				So(order.ChainID, ShouldNotBeEmpty)
				So(order.TokenID, ShouldNotBeEmpty)
				So(order.Amount, ShouldNotBeEmpty)
			}
		})
	})
}
