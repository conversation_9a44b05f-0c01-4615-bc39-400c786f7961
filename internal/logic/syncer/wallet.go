/**
 * @note
 * statement
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package syncer

import (
	"context"
	"fmt"
	"strconv"

	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
)

// startTime 也是左闭区间
func SyncStatementOnceByWalletIDAndChainID(ctx context.Context, host, apiKey, apiSecret, walletID, chainID string, startTimeMilli, endTimeMilli int64) (totalSynced int, latestTimeMilli int64, err error) {
	// 转换时间格式
	startTimeStr := fmt.Sprintf("%d", startTimeMilli)
	endTimeStr := fmt.Sprintf("%d", endTimeMilli)

	for pageNum := int64(1); ; pageNum++ {
		// 调用API获取流水列表
		statements, err := wallet.GetStatementList(ctx, host, apiKey, apiSecret, pageNum, fuseCommon.SyncStatementBatchSize, chainID, walletID, startTimeStr, endTimeStr)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		// 如果没有账单，说明已经拉取完毕，退出循环
		if len(statements) == 0 {
			break
		}

		// 将API返回的账单转换为数据库模型
		dbStatements := make([]*tModel.StatementTable, 0, len(statements))
		for _, item := range statements {
			// 创建数据库表
			dbStatement := &tModel.StatementTable{
				StatementID:     item.ID,
				ReferID:         item.ReferID,
				BlockHeight:     item.BlockHeight,
				BlockHash:       item.BlockHash,
				TransactionHash: item.TransactionHash,
				BlockTimestamp:  item.BlockTimestamp,
				TransferIndex:   item.TransferIndex,
				FromAddress:     item.FromAddress,
				ToAddress:       item.ToAddress,
				WalletID:        item.WalletID,
				ChainID:         item.ChainID,
				TokenID:         item.TokenID,
				Amount:          item.Amount,
				UserTypeBalance: item.UserTypeBalance,
				SysTypeBalance:  item.SysTypeBalance,
				ColdTypeBalance: item.ColdTypeBalance,
				PlatformBalance: item.PlatformBalance,
				Memo:            item.Memo,
				BusinessType:    item.BusinessType,
				State:           item.State,
				ValidateState:   fuseCommon.ValidateStateToBeValidated, // 初始状态为待校验
			}

			// 尝试解析创建时间
			dbStatement.CreatedTimestamp, err = strconv.ParseInt(item.CreatedTimestamp, 10, 64)
			if err != nil {
				return totalSynced, latestTimeMilli, fmt.Errorf("parse statement created_timestamp %s failed: %v", item.CreatedTimestamp, err)
			}
			if latestTimeMilli < dbStatement.CreatedTimestamp {
				latestTimeMilli = dbStatement.CreatedTimestamp
			}

			if dbStatement.BusinessType != fuseCommon.StatementBusinessTypeDeposit &&
				dbStatement.BusinessType != fuseCommon.StatementBusinessTypeWithdraw {
				continue
			} // 只关心充值和提现流水，其他流水不关心

			dbStatements = append(dbStatements, dbStatement)
		}

		// 将账单保存到数据库，支持插入或更新
		err = tModel.CreateOrUpdateStatements(ctx, dbStatements)
		if err != nil {
			return totalSynced, latestTimeMilli, err
		}

		totalSynced += len(dbStatements)

		// 如果返回的数据量小于页大小，说明已经拉取完毕
		if len(statements) < fuseCommon.SyncStatementBatchSize {
			break
		}
	}

	return totalSynced, latestTimeMilli, nil
}
