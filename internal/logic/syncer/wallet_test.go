/**
 * @note
 * wallet_test
 *
 * <AUTHOR>
 * @date 	2025-04-23
 */
package syncer

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"testing"
	"time"

	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/tmsong/hlog"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	fuseConfig "gitlab.docsl.com/security/fuse/pkg/config"
	"gitlab.docsl.com/security/fuse/pkg/helper/wallet"
)

var (
	log *hlog.Logger
	ctx context.Context
)

func init() {
	masker.SetGetKeyFunc(func(arg string) []byte {
		return []byte(os.Getenv("FUSE_CONFIG_CRYPT_KEY"))
	})
	log = hlog.NewLoggerWithConfig(logger.GetHlogConfig(), 0)
	ctx = context.WithValue(context.Background(), common.KeyLogger, log)
	// 尝试加载配置文件，先尝试相对路径，如果失败则尝试绝对路径
	_, err := fuseConfig.ReplaceAndLoad("/root/fuse/conf/pre.toml", true)
	if err != nil {
		panic(err)
	}
}

func Test_GetStatementList(t *testing.T) {
	PatchConvey("Test_GetStatementList", t, func() {
		PatchConvey("case1 GetETH", func() {
			startTime := time.Now().Add(-time.Hour * 24).Unix()
			endTime := time.Now().Unix()
			statements, err := wallet.GetStatementList(ctx, wallet.GetConfig().Host, wallet.GetConfig().ApiKey, wallet.GetConfig().PrivateKey, 1, 100, "eth", "avenir_customer",
				fmt.Sprintf("%d", startTime), fmt.Sprintf("%d", endTime))
			So(err, ShouldBeNil)
			So(statements, ShouldNotBeEmpty)
			str, _ := common.JsonStringEncode(statements)
			fmt.Println(str)
			var latestTime int64
			for _, statement := range statements {
				intTimestamp, err := strconv.ParseInt(statement.CreatedTimestamp, 10, 64)
				So(err, ShouldBeNil)
				if latestTime < intTimestamp {
					latestTime = intTimestamp
				}
				So(statement.ChainID, ShouldEqual, "eth")
				So(statement.TokenID, ShouldNotBeEmpty)
				So(statement.Amount, ShouldNotBeEmpty)
			}
		})
	})
}

func Test_GetWalletList(t *testing.T) {
	PatchConvey("Test_GetWalletList", t, func() {
		PatchConvey("case1 GetWalletList", func() {
			// 调用函数
			wallets, err := wallet.GetWalletList(ctx)
			// 验证结果
			So(err, ShouldBeNil)
			So(wallets, ShouldNotBeEmpty)
			str, _ := common.JsonStringEncode(wallets)
			fmt.Println(str)
			So(str, ShouldBeNil)
		})
	})
}

func Test_FormatAddress(t *testing.T) {
	PatchConvey("Test_FormatAddress", t, func() {
		PatchConvey("case1 FormatAddress", func() {
			// 调用函数
			address, err := wallet.FormatAddress(ctx, "bch", "qzu32mjugv3a32g092fj6wh0s9getwa75gnxtnv94s")
			// 验证结果
			So(err, ShouldBeNil)
			So(address, ShouldNotBeEmpty)
			So(address.FormatAddress, ShouldBeTrue)
			So(address.FormatAddress, ShouldEqual, "**********************************")
		})
	})
}

func Test_GetSupportedChainsAndTokens(t *testing.T) {
	PatchConvey("Test_GetSupportedChainsAndTokens", t, func() {
		PatchConvey("case1 GetSupportedChains", func() {
			// 调用函数
			chains, err := wallet.GetSupportedChains(ctx)
			// 验证结果
			s, _ := common.JsonStringEncode(chains)
			common.GetLogger(ctx).Infoln("chains:", s)
			So(err, ShouldBeNil)
			So(chains, ShouldNotBeEmpty)
		})

		PatchConvey("case2 GetSupportedTokens", func() {
			tokens, err := wallet.GetSupportedTokens(ctx, "eth")
			s, _ := common.JsonStringEncode(tokens)
			common.GetLogger(ctx).Infoln("tokens:", s)
			// 验证结果
			So(err, ShouldBeNil)
			So(tokens, ShouldNotBeEmpty)
		})
	})
}
