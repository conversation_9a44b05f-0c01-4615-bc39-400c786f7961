/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package transaction

import (
	"time"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type TransactionItem struct {
	TransactionID   int64                      `json:"transactionID,string"`
	WalletID        string                     `json:"walletID"`
	ChainID         string                     `json:"chainID"`
	TokenID         string                     `json:"tokenID"`
	TransactionHash string                     `json:"transactionHash"`
	TransferIndex   int64                      `json:"transferIndex"`
	ToAddress       string                     `json:"toAddress"`
	Memo            string                     `json:"memo"`
	TransactionType fuseCommon.TransactionType `json:"transactionType"`
	ValidateState   fuseCommon.ValidateState   `json:"validateState"`
	Statements      []*StatementSubItem        `json:"statements"`
	Orders          []*OrderSubItem            `json:"orders"`
	CreatedAt       time.Time                  `json:"createdAt"`
	UpdatedAt       time.Time                  `json:"updatedAt"`
}

type StatementSubItem struct {
	WalletID       string                           `json:"walletID"`
	StatementID    string                           `json:"statementID"`
	Amount         string                           `json:"amount"`
	StatementType  fuseCommon.StatementBusinessType `json:"statementType"`
	StatementState fuseCommon.StatementState        `json:"statementState"`
	ValidateState  fuseCommon.ValidateState         `json:"validateState"`
	CreatedAt      time.Time                        `json:"createdAt"`
	UpdatedAt      time.Time                        `json:"updatedAt"`
}

type StatementItem struct {
	ChainID         string `json:"chainID"`
	TokenID         string `json:"tokenID"`
	TransactionHash string `json:"transactionHash"`
	TransferIndex   int64  `json:"transferIndex"`
	ToAddress       string `json:"toAddress"`
	Memo            string `json:"memo"`
	StatementSubItem
}

type OrderSubItem struct {
	WalletID      string                   `json:"walletID"`
	OrderID       int64                    `json:"orderID,string"`
	Amount        string                   `json:"amount"`
	OrderType     fuseCommon.OrderType     `json:"orderType"`
	OrderState    string                   `json:"orderState"`
	ValidateState fuseCommon.ValidateState `json:"validateState"`
	CreatedAt     time.Time                `json:"createdAt"`
	UpdatedAt     time.Time                `json:"updatedAt"`
}

type OrderItem struct {
	ChainID            string `json:"chainID"`
	TokenID            string `json:"tokenID"`
	TransactionHash    string `json:"transactionHash"`
	TransferIndex      int64  `json:"transferIndex"`
	ToAddress          string `json:"toAddress"`
	FormattedToAddress string `json:"formattedToAddress,omitempty"`
	Memo               string `json:"memo"`
	OrderSubItem
}
