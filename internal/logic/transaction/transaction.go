/**
 * @note
 * transaction
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package transaction

import (
	// 系统内置包
	"context"
	"gitlab.docsl.com/security/common"
	"strings"
	"time"

	// 开源包
	set "github.com/deckarep/golang-set/v2"

	// gitlab.docsl.com的包
	syncerLogic "gitlab.docsl.com/security/fuse/internal/logic/syncer"
	assetsModel "gitlab.docsl.com/security/fuse/internal/model/assets"
	tModel "gitlab.docsl.com/security/fuse/internal/model/transaction"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryTransactionList 查询交易列表
func QueryTransactionList(ctx context.Context, page, perPage int, transactionIDs []int64, statementID,
	orderID, walletID, chainID, tokenID, transactionHash string, transferIndex *int64,
	toAddress, memo string, transactionType *fuseCommon.TransactionType, validateStates []fuseCommon.ValidateState) (
	items []*TransactionItem, count int64, err error) {

	// 构建查询条件
	filter := tModel.QueryTransactionUnitFilter{
		Page:            page,
		PerPage:         perPage,
		IDs:             transactionIDs,
		StatementID:     statementID,
		OrderID:         orderID,
		WalletID:        walletID,
		ChainID:         chainID,
		TokenID:         tokenID,
		TransactionHash: transactionHash,
		TransferIndex:   transferIndex,
		ToAddress:       toAddress,
		Memo:            memo,
		Type:            transactionType,
		ValidateStates:  validateStates,
		Order:           "created_at desc",
	}

	// 查询交易总数
	count, err = tModel.QueryTransactionUnitCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 如果没有记录，直接返回空结果
	if count == 0 {
		return make([]*TransactionItem, 0), 0, nil
	}

	// 查询交易列表
	units, err := tModel.QueryTransactionUnitsBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 收集所有需要查询的UnitID
	var allUnitIDs []int64

	// 创建映射以便后续组装数据
	unitIDToUnit := make(map[int64]*tModel.TransactionUnitTable)

	for _, unit := range units {
		unitID := int64(unit.ID)
		allUnitIDs = append(allUnitIDs, unitID)
		unitIDToUnit[unitID] = unit
	}

	// 批量查询所有Statement
	allStatements, err := tModel.QueryStatementsBySeveralConditions(ctx, tModel.QueryStatementFilter{
		TransactionUnitIDs: allUnitIDs,
	})
	if err != nil {
		return nil, 0, err
	}

	// 批量查询所有DepositOrder
	allDepositOrders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		TransactionUnitIDs: allUnitIDs,
	})
	if err != nil {
		return nil, 0, err
	}

	// 批量查询所有WithdrawOrder
	allWithdrawOrders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
		TransactionUnitIDs: allUnitIDs,
	})
	if err != nil {
		return nil, 0, err
	}

	// 将Statement和Order按ID映射，方便快速查找
	statementMap := make(map[string]*tModel.StatementTable)
	for _, statement := range allStatements {
		statementMap[statement.StatementID] = statement
	}

	depositOrderMap := make(map[int64]*tModel.DepositOrderTable)
	for _, order := range allDepositOrders {
		depositOrderMap[order.OrderID] = order
	}

	withdrawOrderMap := make(map[int64]*tModel.WithdrawOrderTable)
	for _, order := range allWithdrawOrders {
		withdrawOrderMap[order.OrderID] = order
	}

	// 组装返回结果
	items = make([]*TransactionItem, 0, len(units))
	itemMap := make(map[int64]*TransactionItem)
	tokenSets := set.NewSet[string]()
	for _, unit := range units {
		tokenSets.Add(unit.TokenID)
		item := &TransactionItem{
			TransactionID:   int64(unit.ID),
			WalletID:        unit.WalletID,
			ChainID:         unit.ChainID,
			TokenID:         unit.TokenID,
			TransactionHash: unit.TransactionHash,
			TransferIndex:   unit.TransferIndex,
			ToAddress:       unit.ToAddress,
			Memo:            unit.Memo,
			TransactionType: unit.Type,
			ValidateState:   unit.ValidateState,
			Statements:      make([]*StatementSubItem, 0),
			Orders:          make([]*OrderSubItem, 0),
			CreatedAt:       unit.CreatedAt,
			UpdatedAt:       unit.UpdatedAt,
		}
		itemMap[item.TransactionID] = item
		items = append(items, item)
	}
	err = fixStatementAmount(ctx, allStatements)
	if err != nil {
		return nil, 0, err
	}
	for _, statement := range allStatements {
		if itemMap[statement.TransactionUnitID] != nil {
			itemMap[statement.TransactionUnitID].Statements = append(itemMap[statement.TransactionUnitID].Statements, &StatementSubItem{
				WalletID:       statement.WalletID,
				StatementID:    statement.StatementID,
				Amount:         statement.Amount,
				StatementType:  statement.BusinessType,
				StatementState: statement.State,
				ValidateState:  statement.ValidateState,
				CreatedAt:      time.UnixMilli(statement.CreatedTimestamp),
				UpdatedAt:      time.UnixMilli(statement.CreatedTimestamp),
			})
		}
	}
	// 添加关联的Order
	for _, order := range allDepositOrders {
		if itemMap[order.TransactionUnitID] != nil {
			itemMap[order.TransactionUnitID].Orders = append(itemMap[order.TransactionUnitID].Orders, &OrderSubItem{
				WalletID:      order.WalletID,
				OrderID:       order.OrderID,
				Amount:        order.Amount,
				OrderType:     fuseCommon.OrderTypeDeposit,
				OrderState:    string(order.State),
				ValidateState: order.ValidateState,
				CreatedAt:     order.CreateTime,
				UpdatedAt:     order.UpdateTime,
			})
		}
	}
	for _, order := range allWithdrawOrders {
		if itemMap[order.TransactionUnitID] != nil {
			itemMap[order.TransactionUnitID].Orders = append(itemMap[order.TransactionUnitID].Orders, &OrderSubItem{
				WalletID:      order.WalletID,
				OrderID:       order.OrderID,
				Amount:        order.Amount,
				OrderType:     fuseCommon.OrderTypeWithdraw,
				OrderState:    string(order.State),
				ValidateState: order.ValidateState,
				CreatedAt:     order.CreateTime,
				UpdatedAt:     order.UpdateTime,
			})
		}
	}

	return items, count, nil
}

// SetTransactionValidateState 设置交易校验状态
func SetTransactionValidateState(ctx context.Context, transactionIDs []int64, validateState fuseCommon.ValidateState) error {
	// 验证参数
	if len(transactionIDs) == 0 {
		return nil // 没有提供要更新的ID，直接返回成功
	}

	// 查询要更新的交易记录
	units, err := tModel.QueryTransactionUnitsBySeveralConditions(ctx, tModel.QueryTransactionUnitFilter{
		IDs: transactionIDs,
	})
	if err != nil {
		return err
	}

	// 遍历交易记录并更新状态
	for _, unit := range units {
		unitID := int64(unit.ID)
		// 根据交易类型选择不同的更新方法
		if unit.Type == fuseCommon.TransactionTypeDeposit {
			// 使用事务更新充值交易及关联的订单和流水
			err = tModel.UpdateDepositTransactionUnitByID(ctx, unitID, &validateState, nil, nil, nil, nil)
			if err != nil {
				return err
			}
		} else if unit.Type == fuseCommon.TransactionTypeWithdraw {
			// 使用事务更新提现交易及关联的订单和流水
			err = tModel.UpdateWithdrawTransactionUnitByID(ctx, unitID, &validateState, nil, nil, nil, nil)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// SetStatementValidateState 设置流水校验状态
func SetStatementValidateState(ctx context.Context, statementIDs []string, walletID, chainID string, validateState fuseCommon.ValidateState) error {
	// 验证参数
	if len(statementIDs) == 0 {
		return nil // 没有提供要更新的ID，直接返回成功
	}

	// 更新流水状态
	return tModel.UpdateStatementsByStatementIDsAndWalletIDAndChainID(ctx, statementIDs, walletID, chainID, &validateState, nil)
}

// SetOrderValidateState 设置订单校验状态
func SetOrderValidateState(ctx context.Context, orderIDs []int64, orderType fuseCommon.OrderType, validateState fuseCommon.ValidateState) error {
	// 验证参数
	if len(orderIDs) == 0 {
		return nil // 没有提供要更新的ID，直接返回成功
	}

	if orderType == fuseCommon.OrderTypeDeposit {
		// 分别查询充值订单和提现订单
		depositOrders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
			OrderIDs: orderIDs,
		})
		if err != nil {
			return err
		}
		// 更新充值订单状态
		for _, order := range depositOrders {
			err = tModel.UpdateDepositOrderByOrderID(ctx, order.OrderID, &validateState, nil)
			if err != nil {
				return err
			}
		}
	} else if orderType == fuseCommon.OrderTypeWithdraw {
		withdrawOrders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, tModel.QueryOrderFilter{
			OrderIDs: orderIDs,
		})
		if err != nil {
			return err
		}

		// 更新提现订单状态
		for _, order := range withdrawOrders {
			err = tModel.UpdateWithdrawOrderByOrderID(ctx, order.OrderID, &validateState, nil)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// QueryOrderList 查询订单列表
func QueryOrderList(ctx context.Context, page, perPage int, orderIDs []int64, chainID, tokenID, transactionHash string,
	transferIndex *int64, toAddress, formattedToAddress, memo string, orderType *fuseCommon.OrderType,
	depositStates []fuseCommon.DepositOrderState, withdrawStates []fuseCommon.WithdrawOrderState) (items []*OrderItem, count int64, err error) {

	// 构建查询条件
	filter := tModel.QueryOrderFilter{
		Page:               page,
		PerPage:            perPage,
		OrderIDs:           orderIDs,
		ChainID:            chainID,
		TokenID:            tokenID,
		TransactionHash:    transactionHash,
		TransferIndex:      transferIndex,
		ToAddress:          toAddress,
		FormattedToAddress: formattedToAddress,
		Memo:               memo,
		DepositStates:      depositStates,
		WithdrawStates:     withdrawStates,
		Order:              "created_at desc",
	}

	// 根据订单类型查询不同的表
	if orderType == nil || *orderType == fuseCommon.OrderTypeDeposit {
		// 查询充值订单总数
		depositCount, err := tModel.QueryDepositOrderCountBySeveralConditions(ctx, filter)
		if err != nil {
			return nil, 0, err
		}

		// 如果指定了订单类型为充值或未指定类型，且有充值订单
		if depositCount > 0 {
			// 查询充值订单列表
			depositOrders, err := tModel.QueryDepositOrdersBySeveralConditions(ctx, filter)
			if err != nil {
				return nil, 0, err
			}

			// 将充值订单转换为OrderItem
			for _, order := range depositOrders {
				item := &OrderItem{
					ChainID:         order.ChainID,
					TokenID:         order.TokenID,
					TransactionHash: order.TransactionHash,
					TransferIndex:   order.TransferIndex,
					ToAddress:       order.ToAddress,
					Memo:            order.Memo,
					OrderSubItem: OrderSubItem{
						WalletID:      order.WalletID,
						OrderID:       order.OrderID,
						Amount:        order.Amount,
						OrderType:     fuseCommon.OrderTypeDeposit,
						OrderState:    string(order.State),
						ValidateState: order.ValidateState,
						CreatedAt:     order.CreateTime,
						UpdatedAt:     order.UpdateTime,
					},
				}
				items = append(items, item)
			}

			count = depositCount
		}
	}

	if orderType == nil || *orderType == fuseCommon.OrderTypeWithdraw {
		// 查询提现订单总数
		withdrawCount, err := tModel.QueryWithdrawOrderCountBySeveralConditions(ctx, filter)
		if err != nil {
			return nil, 0, err
		}

		// 如果指定了订单类型为提现或未指定类型，且有提现订单
		if withdrawCount > 0 {
			// 查询提现订单列表
			withdrawOrders, err := tModel.QueryWithdrawOrdersBySeveralConditions(ctx, filter)
			if err != nil {
				return nil, 0, err
			}

			// 将提现订单转换为OrderItem
			for _, order := range withdrawOrders {
				item := &OrderItem{
					ChainID:            order.ChainID,
					TokenID:            order.TokenID,
					TransactionHash:    order.TransactionHash,
					TransferIndex:      order.TransferIndex,
					ToAddress:          order.ToAddress,
					FormattedToAddress: order.FormattedToAddress,
					Memo:               order.Memo,
					OrderSubItem: OrderSubItem{
						WalletID:      order.WalletID,
						OrderID:       order.OrderID,
						Amount:        order.Amount,
						OrderType:     fuseCommon.OrderTypeWithdraw,
						OrderState:    string(order.State),
						ValidateState: order.ValidateState,
						CreatedAt:     order.CreateTime,
						UpdatedAt:     order.UpdateTime,
					},
				}
				items = append(items, item)
			}

			// 如果同时查询了充值和提现订单，需要累加总数
			if orderType == nil {
				count += withdrawCount
			} else {
				count = withdrawCount
			}
		}
	}

	// 如果没有记录，返回空数组
	if len(items) == 0 {
		items = make([]*OrderItem, 0)
	}

	return items, count, nil
}

// QueryStatementList 查询流水列表
func QueryStatementList(ctx context.Context, page, perPage int, statementIDs []string, chainID, tokenID, transactionHash string,
	transferIndex *int64, toAddress, memo string, businessType *fuseCommon.StatementBusinessType,
	state *fuseCommon.StatementState) (items []*StatementItem, count int64, err error) {

	// 构建查询条件
	filter := tModel.QueryStatementFilter{
		Page:            page,
		PerPage:         perPage,
		StatementIDs:    statementIDs,
		ChainID:         chainID,
		TokenID:         tokenID,
		TransactionHash: transactionHash,
		TransferIndex:   transferIndex,
		ToAddress:       toAddress,
		Memo:            memo,
		Order:           "created_timestamp desc",
	}

	// 设置业务类型和状态
	if businessType != nil {
		filter.BusinessType = *businessType
	}
	if state != nil {
		filter.State = state
	}

	// 查询流水总数
	count, err = tModel.QueryStatementCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// 如果没有记录，直接返回空结果
	if count == 0 {
		return make([]*StatementItem, 0), 0, nil
	}

	// 查询流水列表
	statements, err := tModel.QueryStatementsBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	err = fixStatementAmount(ctx, statements)
	if err != nil {
		return nil, 0, err
	}

	// 将流水转换为StatementItem
	items = make([]*StatementItem, 0, len(statements))
	for _, statement := range statements {
		item := &StatementItem{
			ChainID:         statement.ChainID,
			TokenID:         statement.TokenID,
			TransactionHash: statement.TransactionHash,
			TransferIndex:   statement.TransferIndex,
			ToAddress:       statement.ToAddress,
			Memo:            statement.Memo,
			StatementSubItem: StatementSubItem{
				WalletID:       statement.WalletID,
				StatementID:    statement.StatementID,
				Amount:         statement.Amount,
				StatementType:  statement.BusinessType,
				StatementState: statement.State,
				ValidateState:  statement.ValidateState,
				CreatedAt:      time.UnixMilli(statement.CreatedTimestamp),
				UpdatedAt:      time.UnixMilli(statement.CreatedTimestamp),
			},
		}
		items = append(items, item)
	}

	return items, count, nil
}

func fixStatementAmount(ctx context.Context, statements []*tModel.StatementTable) error {
	tokenSets := set.NewSet[string]()
	for _, statement := range statements {
		tokenSets.Add(statement.TokenID)
	}
	if tokenSets.IsEmpty() {
		return nil
	}
	// 查询所有token信息，查出来以后格式化statement里的amount
	tokens, err := assetsModel.QueryTokenBySeveralConditions(ctx, assetsModel.QueryTokenFilter{
		TokenIDs: tokenSets.ToSlice(),
	})
	if err != nil {
		return err
	}
	// 构建tokenMap方便查询
	tokenMap := make(map[string]*assetsModel.TokenTable)
	for _, token := range tokens {
		tokenMap[token.TokenID] = token
	}
	for _, statement := range statements {
		token := tokenMap[statement.TokenID]
		if token != nil && token.Decimals > 0 {
			// 直接对字符串进行处理，避免浮点数计算带来的精度问题
			amountStr := statement.Amount

			// 如果金额为0，直接跳过
			if amountStr == "0" || amountStr == "-0" {
				statement.Amount = "0"
				continue
			}

			// 如果金额已经包含小数点，说明已经是格式化后的金额，直接跳过
			if strings.Contains(amountStr, ".") {
				continue
			}

			// 检查是否为负数
			isNegative := false
			if strings.HasPrefix(amountStr, "-") {
				isNegative = true
				amountStr = amountStr[1:] // 移除负号进行处理
			}

			// 处理金额字符串，根据 decimals 移动小数点
			amountLen := len(amountStr)
			decimals := int(token.Decimals)

			// 如果金额长度小于等于 decimals，需要在前面补零
			if amountLen <= decimals {
				// 补零
				padding := strings.Repeat("0", decimals-amountLen+1)
				amountStr = padding + amountStr
				amountLen = len(amountStr)
			}

			// 插入小数点
			decimalPos := amountLen - decimals
			amountStr = amountStr[:decimalPos] + "." + amountStr[decimalPos:]

			// 去除前导零（但保留小数点前的零）
			if len(amountStr) > 1 && amountStr[0] == '0' && amountStr[1] != '.' {
				amountStr = strings.TrimLeft(amountStr, "0")
			}

			// 如果字符串变成了以小数点开头，添加前导零
			if amountStr != "" && amountStr[0] == '.' {
				amountStr = "0" + amountStr
			}

			// 去除尾部的零和可能的小数点
			amountStr = strings.TrimRight(strings.TrimRight(amountStr, "0"), ".")

			// 如果字符串为空（可能在去除尾部零后），则设置为 "0"
			if amountStr == common.StringEmpty {
				amountStr = "0"
			}

			// 如果原来是负数，且结果不为0，添加负号
			if isNegative && amountStr != "0" {
				amountStr = "-" + amountStr
			}

			statement.Amount = amountStr
		}
	}
	return nil
}

// SyncOrders 同步订单
func SyncOrders(ctx context.Context, startTimeMilli, endTimeMilli int64, orderType fuseCommon.OrderType, chainIDs []string) (totalSynced, latestTimeMilli int64, err error) {
	// 获取默认的钱包配置
	platforms, err := assetsModel.QueryPlatformBySeveralConditions(ctx, assetsModel.QueryPlatformFilter{
		SyncStatus: &[]fuseCommon.SyncStatus{fuseCommon.SyncStatusOn}[0], // 只查询开启同步的平台
	})
	if err != nil {
		return 0, 0, err
	}

	if len(platforms) == 0 {
		return 0, 0, common.ErrRecordNotFound // 没有找到开启同步的平台
	}

	// 使用第一个开启同步的平台的配置
	platform := platforms[0]
	if platform.Config == nil {
		return 0, 0, common.ErrInvalidFieldf("platform.Config")
	}

	host := platform.Config.OrderHost
	if host == common.StringEmpty {
		return 0, 0, common.ErrInvalidFieldf("platform.Config.OrderHost")
	}

	// 根据订单类型调用不同的同步方法
	if orderType == fuseCommon.OrderTypeDeposit {
		return syncerLogic.SyncDepositOrderOnce(ctx, host, chainIDs, startTimeMilli, endTimeMilli)
	} else if orderType == fuseCommon.OrderTypeWithdraw {
		return syncerLogic.SyncWithdrawOrderOnce(ctx, host, chainIDs, startTimeMilli, endTimeMilli)
	} else {
		return 0, 0, common.ErrInvalidFieldf("orderType")
	}
}

// SyncStatements 同步流水
func SyncStatements(ctx context.Context, walletID, chainID string, startTimeMilli, endTimeMilli int64) (totalSynced int, latestTimeMilli int64, err error) {
	// 根据walletID查询平台配置
	platform, err := assetsModel.QueryPlatformByWalletID(ctx, walletID)
	if err != nil {
		return 0, 0, err
	}

	if platform.Config == nil {
		return 0, 0, common.ErrInvalidFieldf("platform.Config")
	}

	host := platform.Config.WalletHost
	apiKey := platform.Config.WalletApiKey
	apiSecret := platform.Config.WalletApiSecret

	if host == common.StringEmpty || apiKey == common.StringEmpty || apiSecret == common.StringEmpty {
		return 0, 0, common.ErrInvalidFieldf("platform.Config")
	}

	return syncerLogic.SyncStatementOnceByWalletIDAndChainID(ctx, host, apiKey, apiSecret, walletID, chainID, startTimeMilli, endTimeMilli)
}
