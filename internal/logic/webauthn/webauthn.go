/**
 * @note
 * webauthn
 *
 * <AUTHOR>
 * @date 	2025-03-03
 */
package webauthn

import (
	"context"
	"errors"

	"github.com/go-webauthn/webauthn/protocol"
	"github.com/go-webauthn/webauthn/webauthn"

	"gitlab.docsl.com/security/common"
	webauthnHelper "gitlab.docsl.com/security/common/webauthn"
	uModel "gitlab.docsl.com/security/fuse/pkg/model/user"
)

func WebauthnRegisterBegin(ctx context.Context) (options *protocol.CredentialCreation, err error) {
	ctxUser := common.GetUser(ctx)
	if ctxUser == nil || ctxUser.ID == common.StringEmpty {
		return nil, common.ErrInvalidFieldf("user")
	}
	userTable, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil { //还是有错，返回
		return nil, err
	}

	options, sessionData, err := webauthnHelper.WebAuthnSignupBegin(ctx, userTable)
	if err != nil {
		return nil, err
	}
	// 存储sessionData
	err = uModel.UpdateUser(ctx, userTable.UserID, nil, nil, nil,
		nil, nil, nil, nil, sessionData)
	if err != nil {
		return nil, err
	}
	return options, nil
}

func WebauthnRegisterFinish(ctx context.Context, reqBody []byte) (err error) {
	ctxUser := common.GetUser(ctx)
	if ctxUser == nil || ctxUser.ID == common.StringEmpty {
		return common.ErrInvalidFieldf("user")
	}
	userTable, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil {
		return err
	}
	var session webauthn.SessionData
	err = common.JsonStringDecode(userTable.WebauthnSession, &session)
	if err != nil {
		return err
	}
	credential, err := webauthnHelper.WebAuthnSignupFinish(ctx, userTable, session, reqBody)
	if err != nil {
		return err
	}
	// 保存credential，先校验是不是已经有现成的了
	ut, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil {
		return err
	}
	if ut.WebauthnCredentials != common.StringEmpty {
		existCredential := make([]*webauthn.Credential, 0)
		err = common.JsonStringDecode(ut.WebauthnCredentials, &existCredential)
		if err != nil {
			return err
		}
		if len(existCredential) > 0 {
			return errors.New("credential exist")
		}
	}
	return uModel.UpdateUser(ctx, userTable.UserID, nil,
		nil, nil, nil, nil, nil, &[]*webauthn.Credential{credential}, nil)
}

func WebauthnValidateBegin(ctx context.Context) (options *protocol.CredentialAssertion, err error) {
	ctxUser := common.GetUser(ctx)
	if ctxUser == nil || ctxUser.ID == common.StringEmpty {
		return nil, common.ErrInvalidFieldf("user")
	}
	userTable, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil { // 有错，返回
		return nil, err
	}

	options, sessionData, err := webauthnHelper.WebAuthnValidateBegin(ctx, userTable)
	if err != nil {
		return nil, err
	}
	// 存储sessionData
	err = uModel.UpdateUser(ctx, userTable.UserID, nil, nil, nil, nil, nil, nil, nil, sessionData)
	if err != nil {
		return nil, err
	}
	return options, nil
}

func WebauthnValidateFinish(ctx context.Context, reqBody []byte) (err error) {
	ctxUser := common.GetUser(ctx)
	if ctxUser == nil || ctxUser.ID == common.StringEmpty {
		return common.ErrInvalidFieldf("user")
	}
	userTable, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
	if err != nil {
		return err
	}
	var session webauthn.SessionData
	err = common.JsonStringDecode(userTable.WebauthnSession, &session)
	if err != nil {
		return err
	}
	_, err = webauthnHelper.WebAuthnValidateFinish(ctx, userTable, session, reqBody)
	return err
}
