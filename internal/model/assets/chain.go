/**
 * @note
 * chain
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package assets

import (
	// 系统内置包
	"context"
	"gorm.io/gorm/clause"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryChainBySeveralConditions 查询Chain列表
func (m *AssetsModelImpl) QueryChainBySeveralConditions(ctx context.Context, filter QueryChainFilter) ([]*ChainTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&ChainTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}

	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryChainConditions(ctx, filter, db)

	tbs := make([]*ChainTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

// QueryChainCountBySeveralConditions 查询Chain数量
func (m *AssetsModelImpl) QueryChainCountBySeveralConditions(ctx context.Context, filter QueryChainFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&ChainTable{})

	db = assembleQueryChainConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

// CreateChains 更新提现订单记录
func (m *AssetsModelImpl) CreateChains(ctx context.Context, chains []*ChainTable) ([]*ChainTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	t := &ChainTable{}
	db = db.Model(t)

	// 生成主键ID
	for _, chain := range chains {
		chain.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "chain_id"}},           // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(&chains, 50).Error
	return chains, db.Error
}

// DeleteChainsByChainIDs 删除Token
func (m *AssetsModelImpl) DeleteChainsByChainIDs(ctx context.Context, chainIDs []string) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&ChainTable{})

	db = db.Where("chain_id in (?)", chainIDs).Delete(&ChainTable{})
	return db.RowsAffected, db.Error
}

// UpdateChainByChainID 更新Chain
func (m *AssetsModelImpl) UpdateChainByChainID(ctx context.Context, chainID string, name, desc *string,
	fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error) {
	if name == nil && desc == nil && fuseType == nil && fuseStatus == nil {
		return nil
	}
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&ChainTable{})
	updateMap := make(map[string]interface{})
	if name != nil {
		updateMap["chain_name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if fuseType != nil {
		updateMap["fuse_type"] = *fuseType
	}
	if fuseStatus != nil {
		updateMap["fuse_status"] = *fuseStatus
	}

	return db.Where("chain_id = ?", chainID).Updates(updateMap).Error
}

func assembleQueryChainConditions(ctx context.Context, filter QueryChainFilter, db *gorm.DB) *gorm.DB {
	// IDs 筛选
	if len(filter.IDs) > 0 {
		db = db.Where("id in (?)", filter.IDs)
	}

	// ChainID 筛选
	if len(filter.ChainIDs) > 1 {
		db = db.Where("chain_id in (?)", filter.ChainIDs)
	} else if len(filter.ChainIDs) == 1 && filter.ChainIDs[0] != StringEmpty {
		db = db.Where("chain_id = ?", filter.ChainIDs[0])
	}

	// FuseType 筛选
	if filter.FuseType != nil {
		db = db.Where("fuse_type = ?", filter.FuseType)
	}

	// FuseStatus 筛选
	if filter.FuseStatus != nil {
		db = db.Where("fuse_status = ?", filter.FuseStatus)
	}

	return db
}

// QueryChainMaxUpdateTime 查询Chain表的最大更新时间
func (m *AssetsModelImpl) QueryChainMaxUpdateTime(ctx context.Context) (time.Time, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return time.Time{}, err
	}
	db = db.Model(&ChainTable{})
	db = db.Select("MAX(updated_at) as max_updated_at")
	var result struct {
		MaxUpdatedAt time.Time `gorm:"column:max_updated_at"`
	}
	db = db.Scan(&result)
	if db.Error != nil {
		return time.Time{}, db.Error
	}
	return result.MaxUpdatedAt, nil
}
