/**
 * @note
 * platform
 *
 * <AUTHOR>
 * @date 	2025-05-08
 */
package assets

import (
	// 系统内置包
	"context"
	// 开源包
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryPlatformBySeveralConditions 查询Platform列表
func (m *AssetsModelImpl) QueryPlatformBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) ([]*PlatformTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&PlatformTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}

	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryPlatformConditions(ctx, filter, db)

	tbs := make([]*PlatformTable, 0)
	db = db.Find(&tbs)
	return tbs, db.Error
}

// QueryPlatformCountBySeveralConditions 查询Platform数量
func (m *AssetsModelImpl) QueryPlatformCountBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&PlatformTable{})

	db = assembleQueryPlatformConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

// CreatePlatforms 创建Platform
func (m *AssetsModelImpl) CreatePlatforms(ctx context.Context, platforms []*PlatformTable) ([]*PlatformTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	t := &PlatformTable{}
	db = db.Model(t)

	// 生成主键ID
	for _, platform := range platforms {
		platform.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "wallet_id"}},          // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(&platforms, 50).Error
	return platforms, db.Error
}

// DeletePlatformsByWalletIDs 删除Platform
func (m *AssetsModelImpl) DeletePlatformsByWalletIDs(ctx context.Context, walletIDs []string) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&PlatformTable{})

	db = db.Where("wallet_id in (?)", walletIDs).Delete(&PlatformTable{})
	return db.RowsAffected, db.Error
}

// UpdatePlatformByWalletID 更新Platform
func (m *AssetsModelImpl) UpdatePlatformByWalletID(ctx context.Context, walletID string, name, desc *string, config *PlatformConfig,
	syncStatus *fuseCommon.SyncStatus) (err error) {
	if name == nil && desc == nil && config == nil && syncStatus == nil {
		return nil
	}
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&PlatformTable{})
	updateMap := make(map[string]interface{})
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if config != nil {
		updateMap["config"] = config
	}
	if syncStatus != nil {
		updateMap["sync_status"] = *syncStatus
	}

	return db.Where("wallet_id = ?", walletID).Updates(updateMap).Error
}

func assembleQueryPlatformConditions(ctx context.Context, filter QueryPlatformFilter, db *gorm.DB) *gorm.DB {
	// WalletIDs 筛选
	if len(filter.WalletIDs) > 1 {
		db = db.Where("wallet_id in (?)", filter.WalletIDs)
	} else if len(filter.WalletIDs) == 1 {
		db = db.Where("wallet_id = ?", filter.WalletIDs[0])
	}

	// SyncStatus 筛选
	if filter.SyncStatus != nil {
		db = db.Where("sync_status = ?", filter.SyncStatus)
	}

	return db
}
