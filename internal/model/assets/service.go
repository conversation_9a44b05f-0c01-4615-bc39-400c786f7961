/**
 * @note
 * service
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package assets

import (
	// 系统内置包
	"context"
	"fmt"
	"gitlab.docsl.com/security/common/redis"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type AssetsModel interface {
	// QueryChainBySeveralConditions 查询Chain列表
	QueryChainBySeveralConditions(ctx context.Context, filter QueryChainFilter) ([]*ChainTable, error)
	// QueryChainCountBySeveralConditions 查询Chain数量
	QueryChainCountBySeveralConditions(ctx context.Context, filter QueryChainFilter) (int64, error)
	// CreateChains 更新提现订单记录
	CreateChains(ctx context.Context, chains []*ChainTable) ([]*ChainTable, error)
	// DeleteChainsByChainIDs 删除Token
	DeleteChainsByChainIDs(ctx context.Context, chainIDs []string) (int64, error)
	// UpdateChainByChainID 更新Chain
	UpdateChainByChainID(ctx context.Context, chainID string, name, desc *string,
		fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error)
	// QueryChainMaxUpdateTime 查询Chain表的最大更新时间
	QueryChainMaxUpdateTime(ctx context.Context) (time.Time, error)

	// QueryTokenBySeveralConditions 查询Token列表
	QueryTokenBySeveralConditions(ctx context.Context, filter QueryTokenFilter) ([]*TokenTable, error)
	// QueryTokenCountBySeveralConditions	查询Token数量
	QueryTokenCountBySeveralConditions(ctx context.Context, filter QueryTokenFilter) (int64, error)
	// CreateTokens 创建Token
	CreateTokens(ctx context.Context, tokens []*TokenTable) ([]*TokenTable, error)
	// DeleteTokensByTokenIDs 删除Tokens
	DeleteTokensByTokenIDs(ctx context.Context, tokenIDs []string) (int64, error)
	// UpdateTokenByTokenID 更新Token
	UpdateTokenByTokenID(ctx context.Context, tokenID string, chainID, name, desc *string, decimals *int64,
		fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error)
	// QueryTokenMaxUpdateTime 查询Token表的最大更新时间
	QueryTokenMaxUpdateTime(ctx context.Context) (time.Time, error)

	// QueryPlatformBySeveralConditions 查询Platform列表
	QueryPlatformBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) ([]*PlatformTable, error)
	// QueryPlatformCountBySeveralConditions 查询Platform数量
	QueryPlatformCountBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) (int64, error)
	// CreatePlatforms 创建Platform
	CreatePlatforms(ctx context.Context, platforms []*PlatformTable) ([]*PlatformTable, error)
	// DeletePlatformsByWalletIDs 删除Platform
	DeletePlatformsByWalletIDs(ctx context.Context, walletIDs []string) (int64, error)
	// UpdatePlatformByWalletID 更新Platform
	UpdatePlatformByWalletID(ctx context.Context, walletID string, name, desc *string, config *PlatformConfig,
		syncStatus *fuseCommon.SyncStatus) (err error)
}

type AssetsModelImpl struct{}

func (m *AssetsModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService AssetsModel = &AssetsModelImpl{}

// QueryChainBySeveralConditions 查询Chain列表
func QueryChainBySeveralConditions(ctx context.Context, filter QueryChainFilter) ([]*ChainTable, error) {
	return DefaultService.QueryChainBySeveralConditions(ctx, filter)
}

// QueryChainCountBySeveralConditions 查询Chain数量
func QueryChainCountBySeveralConditions(ctx context.Context, filter QueryChainFilter) (int64, error) {
	return DefaultService.QueryChainCountBySeveralConditions(ctx, filter)
}

// QueryChainByChainID 根据chainID查询chain信息
func QueryChainByChainID(ctx context.Context, chainID string) (*ChainTable, error) {
	cacheKey := fmt.Sprintf(fuseCommon.ChainCacheKeyFormat, chainID)
	tb := &ChainTable{}
	redisClient, errRedis := redis.DefaultClient(ctx)
	if errRedis == nil {
		redisClient.Printlog(true)
		ret, errGet := redisClient.Get(cacheKey) // 查缓存
		if errGet == nil {
			if errJson := common.JsonStringDecode(ret.Val(), tb); errJson == nil {
				if tb.ID == 0 { // 找不到结果，也缓存，防止击穿
					return nil, common.ErrRecordNotFound
				}
				return tb, nil
			}
		}
	} else {
		common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
	}
	tbs, err := DefaultService.QueryChainBySeveralConditions(ctx, QueryChainFilter{
		ChainIDs: []string{chainID},
	})
	if err != nil {
		return nil, err
	}
	if len(tbs) == 0 {
		if errRedis == nil {
			_ = redisClient.Set(cacheKey, common.StringJsonEmpty, fuseCommon.ChainCacheExpirations)
		}
		return nil, common.ErrRecordNotFound
	}
	tb = tbs[0]
	tbString, errJson := common.JsonStringEncode(tb)
	if errJson == nil {
		if errRedis == nil {
			_ = redisClient.Set(cacheKey, tbString, fuseCommon.ChainCacheExpirations)
		}
	}
	return tb, nil
}

// CreateChains 更新提现订单记录
func CreateChains(ctx context.Context, chains []*ChainTable) ([]*ChainTable, error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			for _, chain := range chains {
				cacheKey := fmt.Sprintf(fuseCommon.ChainCacheKeyFormat, chain.ChainID)
				_, _ = redisClient.Del(cacheKey)
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.CreateChains(ctx, chains)
}

// DeleteChainsByChainIDs 删除Token
func DeleteChainsByChainIDs(ctx context.Context, chainIDs []string) (int64, error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			for _, chainID := range chainIDs {
				cacheKey := fmt.Sprintf(fuseCommon.ChainCacheKeyFormat, chainID)
				_, _ = redisClient.Del(cacheKey)
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.DeleteChainsByChainIDs(ctx, chainIDs)
}

// UpdateChainByChainID 更新Chain
func UpdateChainByChainID(ctx context.Context, chainID string, name, desc *string,
	fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			cacheKey := fmt.Sprintf(fuseCommon.ChainCacheKeyFormat, chainID)
			_, _ = redisClient.Del(cacheKey)
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.UpdateChainByChainID(ctx, chainID, name, desc, fuseType, fuseStatus)
}

// QueryTokenBySeveralConditions 查询Token列表
func QueryTokenBySeveralConditions(ctx context.Context, filter QueryTokenFilter) ([]*TokenTable, error) {
	return DefaultService.QueryTokenBySeveralConditions(ctx, filter)
}

// QueryTokenCountBySeveralConditions 查询Token数量
func QueryTokenCountBySeveralConditions(ctx context.Context, filter QueryTokenFilter) (int64, error) {
	return DefaultService.QueryTokenCountBySeveralConditions(ctx, filter)
}

// QueryTokenByTokenID 根据tokenID查询token信息
func QueryTokenByTokenID(ctx context.Context, tokenID string) (*TokenTable, error) {
	cacheKey := fmt.Sprintf(fuseCommon.TokenCacheKeyFormat, tokenID)
	tb := &TokenTable{}
	redisClient, errRedis := redis.DefaultClient(ctx)
	if errRedis == nil {
		redisClient.Printlog(true)
		ret, errGet := redisClient.Get(cacheKey) // 查缓存
		if errGet == nil {
			if errJson := common.JsonStringDecode(ret.Val(), tb); errJson == nil {
				if tb.ID == 0 { // 找不到结果，也缓存，防止击穿
					return nil, common.ErrRecordNotFound
				}
				return tb, nil
			}
		}
	} else {
		common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
	}
	tbs, err := DefaultService.QueryTokenBySeveralConditions(ctx, QueryTokenFilter{
		TokenIDs: []string{tokenID},
	})
	if err != nil {
		return nil, err
	}
	if len(tbs) == 0 {
		if errRedis == nil {
			_ = redisClient.Set(cacheKey, common.StringJsonEmpty, fuseCommon.TokenCacheExpirations)
		}
		return nil, common.ErrRecordNotFound
	}
	tb = tbs[0]
	tbString, errJson := common.JsonStringEncode(tb)
	if errJson == nil {
		if errRedis == nil {
			_ = redisClient.Set(cacheKey, tbString, fuseCommon.TokenCacheExpirations)
		}
	}
	return tb, nil
}

// CreateTokens 创建Token
func CreateTokens(ctx context.Context, tokens []*TokenTable) ([]*TokenTable, error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			for _, token := range tokens {
				cacheKey := fmt.Sprintf(fuseCommon.ChainCacheKeyFormat, token.TokenID)
				_, _ = redisClient.Del(cacheKey)
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.CreateTokens(ctx, tokens)
}

// DeleteTokensByTokenIDs 删除Tokens
func DeleteTokensByTokenIDs(ctx context.Context, tokenIDs []string) (int64, error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			for _, tokenID := range tokenIDs {
				cacheKey := fmt.Sprintf(fuseCommon.TokenCacheKeyFormat, tokenID)
				_, _ = redisClient.Del(cacheKey)
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.DeleteTokensByTokenIDs(ctx, tokenIDs)
}

// UpdateTokenByTokenID 更新Token
func UpdateTokenByTokenID(ctx context.Context, tokenID string, chainID, name, desc *string, decimals *int64,
	fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error) {
	defer func() {
		// 清除缓存
		if redisClient, errRedis := redis.DefaultClient(ctx); errRedis == nil {
			redisClient.Printlog(true)
			cacheKey := fmt.Sprintf(fuseCommon.TokenCacheKeyFormat, tokenID)
			_, _ = redisClient.Del(cacheKey)
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", errRedis)
		}
	}()
	return DefaultService.UpdateTokenByTokenID(ctx, tokenID, chainID, name, desc, decimals, fuseType, fuseStatus)
}

// QueryChainMaxUpdateTime 查询Chain表的最大更新时间
func QueryChainMaxUpdateTime(ctx context.Context) (time.Time, error) {
	return DefaultService.QueryChainMaxUpdateTime(ctx)
}

// QueryTokenMaxUpdateTime 查询Token表的最大更新时间
func QueryTokenMaxUpdateTime(ctx context.Context) (time.Time, error) {
	return DefaultService.QueryTokenMaxUpdateTime(ctx)
}

// QueryPlatformBySeveralConditions 查询Platform列表
func QueryPlatformBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) ([]*PlatformTable, error) {
	return DefaultService.QueryPlatformBySeveralConditions(ctx, filter)
}

// QueryPlatformCountBySeveralConditions 查询Platform数量
func QueryPlatformCountBySeveralConditions(ctx context.Context, filter QueryPlatformFilter) (int64, error) {
	return DefaultService.QueryPlatformCountBySeveralConditions(ctx, filter)
}

// QueryPlatformByWalletID 根据walletID查询platform信息
func QueryPlatformByWalletID(ctx context.Context, walletID string) (*PlatformTable, error) {
	tbs, err := DefaultService.QueryPlatformBySeveralConditions(ctx, QueryPlatformFilter{
		WalletIDs: []string{walletID},
	})
	if err != nil {
		return nil, err
	}
	if len(tbs) == 0 {
		return nil, common.ErrRecordNotFound
	}
	return tbs[0], nil
}

// CreatePlatforms 创建Platform
func CreatePlatforms(ctx context.Context, platforms []*PlatformTable) ([]*PlatformTable, error) {
	return DefaultService.CreatePlatforms(ctx, platforms)
}

// DeletePlatformsByWalletIDs 删除Platform
func DeletePlatformsByWalletIDs(ctx context.Context, walletIDs []string) (int64, error) {
	return DefaultService.DeletePlatformsByWalletIDs(ctx, walletIDs)
}

// UpdatePlatformByWalletID 更新Platform
func UpdatePlatformByWalletID(ctx context.Context, walletID string, name, desc *string, config *PlatformConfig,
	syncStatus *fuseCommon.SyncStatus) (err error) {
	return DefaultService.UpdatePlatformByWalletID(ctx, walletID, name, desc, config, syncStatus)
}
