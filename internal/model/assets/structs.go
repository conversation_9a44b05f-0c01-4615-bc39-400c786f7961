/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-03-31
 */
package assets

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// ChainTable 链的基本信息
type ChainTable struct {
	gorm.Model
	ChainID    string                `gorm:"column:chain_id"`    // 链ID
	ChainName  string                `gorm:"column:chain_name"`  // 链名称
	Desc       string                `gorm:"column:desc"`        // 链描述
	FuseType   fuseCommon.FuseType   `gorm:"column:fuse_type"`   // 熔断类型，0为人工，1为自动
	FuseStatus fuseCommon.FuseStatus `gorm:"column:fuse_status"` // 当前熔断状态，0为未熔断，1为熔断
}

// TokenTable 币-链的信息
type TokenTable struct {
	gorm.Model
	TokenID    string                `gorm:"column:token_id"`    // 币种ID
	ChainID    string                `gorm:"column:chain_id"`    // 链ID
	TokenName  string                `gorm:"column:token_name"`  // 币种名称
	Decimals   int64                 `gorm:"column:decimals"`    // 小数点
	Desc       string                `gorm:"column:desc"`        // 币种描述
	FuseType   fuseCommon.FuseType   `gorm:"column:fuse_type"`   // 熔断类型，0为人工，1为自动
	FuseStatus fuseCommon.FuseStatus `gorm:"column:fuse_status"` // 当前熔断状态，0为未熔断，1为熔断
}

func (t *TokenTable) TableName() string {
	return fuseCommon.TokenTableName
}

func (t *ChainTable) TableName() string {
	return fuseCommon.ChainTableName
}

func (t *ChainTable) OnUpdateColumns() []string {
	return []string{"id", "chain_name", "desc", "fuse_type",
		"fuse_status", "created_at", "updated_at", "deleted_at"}
}

func (t *TokenTable) OnUpdateColumns() []string {
	return []string{"id", "token_name", "decimals", "desc", "fuse_type",
		"fuse_status", "created_at", "updated_at", "deleted_at"}
}

type QueryChainFilter struct {
	Page, PerPage int
	Order         string
	IDs           []int64
	ChainIDs      []string
	FuseType      *fuseCommon.FuseType
	FuseStatus    *fuseCommon.FuseStatus
}

type QueryTokenFilter struct {
	Page, PerPage int
	Order         string
	IDs           []int64
	TokenIDs      []string
	ChainIDs      []string
	FuseType      *fuseCommon.FuseType
	FuseStatus    *fuseCommon.FuseStatus
}

type QueryPlatformFilter struct {
	Page, PerPage int
	WalletIDs     []string
	SyncStatus    *fuseCommon.SyncStatus
}

// PlatformTable 平台的基本信息
type PlatformTable struct {
	gorm.Model
	WalletID   string                `gorm:"column:wallet_id"`   // 平台walletID
	Name       string                `gorm:"column:name"`        // 平台名称
	Desc       string                `gorm:"column:desc"`        // 平台描述
	Config     *PlatformConfig       `gorm:"column:config"`      // 平台的Api Config
	SyncStatus fuseCommon.SyncStatus `gorm:"column:sync_status"` // 当前同步状态，0为未开启同步，1为开启同步
}

func (t *PlatformTable) TableName() string {
	return fuseCommon.PlatformTableName
}

func (t *PlatformTable) OnUpdateColumns() []string {
	return []string{"id", "name", "desc", "created_at", "updated_at", "deleted_at"}
}

type PlatformConfig struct {
	WalletHost      string `json:"walletHost"`
	OrderHost       string `json:"orderHost"`
	WalletApiKey    string `json:"walletApiKey" mask:"crypt"`
	WalletApiSecret string `json:"walletApiSecret" mask:"crypt"`
}

func (o *PlatformConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o *PlatformConfig) Value() (driver.Value, error) {
	if o == nil {
		return nil, nil
	}
	return json.Marshal(o)
}
