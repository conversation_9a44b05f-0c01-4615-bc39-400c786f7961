/**
 * @note
 * token
 *
 * <AUTHOR>
 * @date 	2025-04-07
 */
package assets

import (
	// 系统内置包
	"context"
	"gorm.io/gorm/clause"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (m *AssetsModelImpl) QueryTokenBySeveralConditions(ctx context.Context, filter QueryTokenFilter) ([]*TokenTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&TokenTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryTokenConditions(ctx, filter, db)

	tbs := make([]*TokenTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *AssetsModelImpl) QueryTokenCountBySeveralConditions(ctx context.Context, filter QueryTokenFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&TokenTable{})

	db = assembleQueryTokenConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *AssetsModelImpl) CreateTokens(ctx context.Context, tokens []*TokenTable) ([]*TokenTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	t := &TokenTable{}
	db = db.Model(t)

	// 生成主键ID
	for _, token := range tokens {
		token.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "chain_id"}, {Name: "token_id"}}, // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()),           // 冲突时更新这些字段
	}).CreateInBatches(&tokens, 50).Error
	return tokens, db.Error
}

func (m *AssetsModelImpl) DeleteTokensByTokenIDs(ctx context.Context, tokenIDs []string) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&TokenTable{})

	db = db.Where("token_id in (?)", tokenIDs).Delete(&TokenTable{})
	return db.RowsAffected, db.Error
}

func (m *AssetsModelImpl) UpdateTokenByTokenID(ctx context.Context, tokenID string,
	chainID, name, desc *string, decimals *int64, fuseType *fuseCommon.FuseType, fuseStatus *fuseCommon.FuseStatus) (err error) {
	if chainID == nil && name == nil && desc == nil && fuseType == nil && fuseStatus == nil {
		return nil
	}
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&TokenTable{})
	updateMap := make(map[string]interface{})
	if fuseType != nil {
		updateMap["fuse_type"] = *fuseType
	}
	if fuseStatus != nil {
		updateMap["fuse_status"] = *fuseStatus
	}
	if chainID != nil {
		updateMap["chain_id"] = *chainID
	}
	if name != nil {
		updateMap["token_name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if decimals != nil {
		updateMap["decimals"] = *decimals
	}

	return db.Where("token_id = ?", tokenID).Updates(updateMap).Error
}

func assembleQueryTokenConditions(ctx context.Context, filter QueryTokenFilter, db *gorm.DB) *gorm.DB {
	// IDs 筛选
	if len(filter.IDs) > 0 {
		db = db.Where("id in (?)", filter.IDs)
	}

	// TokenID 筛选
	if len(filter.TokenIDs) == 1 {
		db = db.Where("token_id = ?", filter.TokenIDs[0])
	} else if len(filter.TokenIDs) > 1 {
		db = db.Where("token_id in (?)", filter.TokenIDs)
	}
	// ChainID 筛选
	if len(filter.ChainIDs) == 1 {
		db = db.Where("chain_id = ?", filter.ChainIDs[0])
	} else if len(filter.ChainIDs) > 1 {
		db = db.Where("chain_id in (?)", filter.ChainIDs)
	}

	// FuseType 筛选
	if filter.FuseType != nil {
		db = db.Where("fuse_type = ?", filter.FuseType)
	}

	// FuseStatus 筛选
	if filter.FuseStatus != nil {
		db = db.Where("fuse_status = ?", filter.FuseStatus)
	}

	return db
}

// QueryTokenMaxUpdateTime 查询Token表的最大更新时间
func (m *AssetsModelImpl) QueryTokenMaxUpdateTime(ctx context.Context) (time.Time, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return time.Time{}, err
	}
	db = db.Model(&TokenTable{})
	db = db.Select("MAX(updated_at) as max_updated_at")
	var result struct {
		MaxUpdatedAt time.Time `gorm:"column:max_updated_at"`
	}
	db = db.Scan(&result)
	if db.Error != nil {
		return time.Time{}, db.Error
	}
	return result.MaxUpdatedAt, nil
}
