/**
 * @note
 * relation
 *
 * <AUTHOR>
 * @date 	2025-04-01
 */
package rule

import (
	// 系统内置包
	"context"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (m *RuleModelImpl) QueryFuseRuleRelationBySeveralConditions(ctx context.Context, filter QueryFuseRuleRelationFilter) ([]*RuleRelationTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&RuleRelationTable{})

	db = assembleQueryRelationConditions(ctx, filter, db)

	tbs := make([]*RuleRelationTable, 0)

	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *RuleModelImpl) CreateFuseRuleRelations(ctx context.Context, relations []*RuleRelationTable) ([]*RuleRelationTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&RuleRelationTable{})

	// 生成主键ID
	for _, relation := range relations {
		relation.ID = uint(idgen.GetID())
	}

	db = db.CreateInBatches(&relations, 50)
	return relations, db.Error
}

func (m *RuleModelImpl) DeleteFuseRuleRelationsByRelationIDs(ctx context.Context,
	relationIDs []int64) (rowsAffected int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleRelationTable{})

	db = db.Where("id in (?)", relationIDs).Delete(&RuleRelationTable{})
	return db.RowsAffected, db.Error
}

func (m *RuleModelImpl) DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx context.Context,
	assetType fuseCommon.AssetType, assetIDs []int64) (rowsAffected int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleRelationTable{})

	db = db.Where("asset_type = ? and asset_id in (?)", assetType, assetIDs).Delete(&RuleRelationTable{})
	return db.RowsAffected, db.Error
}

func (m *RuleModelImpl) DeleteFuseRuleRelationsByRuleIDs(ctx context.Context,
	ruleIDs []int64) (rowsAffected int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleRelationTable{})

	db = db.Where("rule_id in (?)", ruleIDs).Delete(&RuleRelationTable{})
	return db.RowsAffected, db.Error
}

func assembleQueryRelationConditions(ctx context.Context, filter QueryFuseRuleRelationFilter, db *gorm.DB) *gorm.DB {
	// 任务ID筛选
	if len(filter.RuleIDs) > 1 {
		db = db.Where("rule_id in (?)", filter.RuleIDs)
	} else if len(filter.RuleIDs) == 1 && filter.RuleIDs[0] > 0 {
		db = db.Where("rule_id = ?", filter.RuleIDs[0])
	}

	// 资产ID筛选
	if len(filter.AssetIDs) > 1 {
		db = db.Where("asset_type = ? and asset_id in (?)", filter.AssetType, filter.AssetIDs)
	} else if len(filter.AssetIDs) == 1 && filter.AssetIDs[0] > 0 {
		db = db.Where("asset_type = ? and asset_id = ?", filter.AssetType, filter.AssetIDs[0])
	}

	return db
}

// QueryRuleRelationMaxUpdateTime 查询规则关系表最大更新时间
func (m *RuleModelImpl) QueryRuleRelationMaxUpdateTime(ctx context.Context) (time.Time, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return time.Time{}, err
	}
	db = db.Model(&RuleRelationTable{})
	db = db.Select("MAX(updated_at) as max_updated_at")
	var result struct {
		MaxUpdatedAt time.Time `gorm:"column:max_updated_at"`
	}
	db = db.Scan(&result)
	if db.Error != nil {
		return time.Time{}, db.Error
	}
	return result.MaxUpdatedAt, nil
}

// QueryRuleRelationTotalCount 查询规则关系表总数
func (m *RuleModelImpl) QueryRuleRelationTotalCount(ctx context.Context) (int64, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleRelationTable{})
	var count int64
	db = db.Count(&count)
	return count, db.Error
}
