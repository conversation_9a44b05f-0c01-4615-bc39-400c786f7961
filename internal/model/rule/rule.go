/**
 * @note
 * rule
 *
 * <AUTHOR>
 * @date 	2025-04-01
 */
package rule

import (
	// 系统内置包
	"context"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (m *RuleModelImpl) QueryFuseRuleCountBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleTable{})

	db = assembleQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func assembleQueryConditions(ctx context.Context, filter QueryFuseRuleFilter, db *gorm.DB) *gorm.DB {
	// 任务ID筛选
	if len(filter.RuleIDs) > 1 {
		db = db.Where("id in (?)", filter.RuleIDs)
	} else if len(filter.RuleIDs) == 1 && filter.RuleIDs[0] > 0 {
		db = db.Where("id = ?", filter.RuleIDs[0])
	}

	// 状态筛选
	if filter.Status != nil {
		db = db.Where("status = ?", filter.Status)
	}

	return db
}

func (m *RuleModelImpl) QueryFuseRuleBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) ([]*RuleTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&RuleTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryConditions(ctx, filter, db)

	tbs := make([]*RuleTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	} else {
		db = db.Order("priority desc")
	}

	if len(filter.SelectFields) > 0 {
		db = db.Select(filter.SelectFields)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *RuleModelImpl) CreateFuseRule(ctx context.Context, name, desc string, priority int64, detail *RuleDetail,
	actions *ActionDetail) (ruleID int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&RuleTable{})
	tb := &RuleTable{
		Name:     name,
		Desc:     desc,
		Priority: priority,
		Status:   fuseCommon.RuleStatusDisabled, // 创建时都为未启用
		Detail:   detail,
		Actions:  actions,
	}
	id := idgen.GetID()
	tb.ID = uint(id) // 生成唯一ID
	return id, db.Create(tb).Error
}

func (m *RuleModelImpl) DeleteFuseRule(ctx context.Context, ruleIDs []int64) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&RuleTable{})

	db = db.Where("id in (?)", ruleIDs).Delete(&RuleTable{})
	return db.Error
}

// UpdateFuseRuleByRuleID 更新规则记录
func (m *RuleModelImpl) UpdateFuseRuleByRuleID(ctx context.Context, ruleID int64, name, desc *string, priority *int64,
	status *fuseCommon.RuleStatus, detail *RuleDetail,
	actions *ActionDetail) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&RuleTable{})
	updateMap := make(map[string]interface{})
	if name != nil {
		updateMap["name"] = *name
	}
	if desc != nil {
		updateMap["desc"] = *desc
	}
	if priority != nil {
		updateMap["priority"] = *priority
	}
	if status != nil {
		updateMap["status"] = *status
	}
	if detail != nil {
		detailStr, err := common.JsonStringEncode(detail)
		if err != nil {
			return err
		}
		updateMap["detail"] = detailStr
	}
	if actions != nil {
		actionsStr, err := common.JsonStringEncode(actions)
		if err != nil {
			return err
		}
		updateMap["actions"] = actionsStr
	}
	return db.Where("id = ?", ruleID).Updates(updateMap).Error
}

// QueryRuleMaxUpdateTime 查询规则表最大更新时间
func (m *RuleModelImpl) QueryRuleMaxUpdateTime(ctx context.Context) (time.Time, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return time.Time{}, err
	}
	db = db.Model(&RuleTable{})
	db = db.Select("MAX(updated_at) as max_updated_at")
	var result struct {
		MaxUpdatedAt time.Time `gorm:"column:max_updated_at"`
	}
	db = db.Scan(&result)
	if db.Error != nil {
		return time.Time{}, db.Error
	}
	return result.MaxUpdatedAt, nil
}

