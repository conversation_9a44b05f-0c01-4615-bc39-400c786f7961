/**
 * @note
 * service
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package rule

import (
	// 系统内置包
	"context"
	"time"

	// 开源包
	"gorm.io/gorm"

	// gitlab.docsl.com的包
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type RuleModel interface {
	// QueryFuseRuleBySeveralConditions 根据多个条件查询Rule列表
	QueryFuseRuleBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) ([]*RuleTable, error)
	// QueryFuseRuleCountBySeveralConditions 根据多个条件查询Rule总量
	QueryFuseRuleCountBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) (int64, error)
	// CreateFuseRule 插入规则
	CreateFuseRule(ctx context.Context, name, desc string, priority int64, detail *RuleDetail,
		actions *ActionDetail) (ruleID int64, err error)
	// UpdateFuseRuleByRuleID 更新规则记录
	UpdateFuseRuleByRuleID(ctx context.Context, ruleID int64, name, desc *string, priority *int64,
		status *fuseCommon.RuleStatus, detail *RuleDetail, actions *ActionDetail) (err error)
	// DeleteFuseRule 删除规则记录
	DeleteFuseRule(ctx context.Context, ruleIDs []int64) (err error)
	// QueryFuseRuleRelationBySeveralConditions 查询rule与资产ID绑定关系
	QueryFuseRuleRelationBySeveralConditions(ctx context.Context, filter QueryFuseRuleRelationFilter) ([]*RuleRelationTable, error)
	// CreateFuseRuleRelations 创建rule与资产ID绑定关系
	CreateFuseRuleRelations(ctx context.Context, relations []*RuleRelationTable) ([]*RuleRelationTable, error)
	// DeleteFuseRuleRelationsByRelationIDs 删除rule与资产ID绑定关系
	DeleteFuseRuleRelationsByRelationIDs(ctx context.Context, relationIDs []int64) (int64, error)
	// DeleteFuseRuleRelationsByAssetTypeAndAssetIDs 根据资产ID列表，删除rule与资产ID绑定关系
	DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx context.Context,
		assetType fuseCommon.AssetType, assetIDs []int64) (rowsAffected int64, err error)
	// DeleteFuseRuleRelationsByRuleIDs 根据规则ID列表，删除rule与资产ID绑定关系
	DeleteFuseRuleRelationsByRuleIDs(ctx context.Context, ruleIDs []int64) (rowsAffected int64, err error)
	// QueryRuleMaxUpdateTime 查询rule最大更新时间
	QueryRuleMaxUpdateTime(ctx context.Context) (time.Time, error)
	// QueryRuleRelationMaxUpdateTime 查询rule绑定关系最大更新时间
	QueryRuleRelationMaxUpdateTime(ctx context.Context) (time.Time, error)
	// QueryRuleRelationTotalCount 查询rule绑定关系总数
	QueryRuleRelationTotalCount(ctx context.Context) (int64, error)
}

type RuleModelImpl struct{}

func (m *RuleModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService RuleModel = &RuleModelImpl{}

// QueryFuseRuleBySeveralConditions 根据多个条件查询Rule列表
func QueryFuseRuleBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) ([]*RuleTable, error) {
	return DefaultService.QueryFuseRuleBySeveralConditions(ctx, filter)
}

// QueryFuseRuleCountBySeveralConditions 根据多个条件查询Rule总量
func QueryFuseRuleCountBySeveralConditions(ctx context.Context, filter QueryFuseRuleFilter) (int64, error) {
	return DefaultService.QueryFuseRuleCountBySeveralConditions(ctx, filter)
}

// CreateFuseRule 插入规则
func CreateFuseRule(ctx context.Context, name, desc string, priority int64, detail *RuleDetail,
	actions *ActionDetail) (ruleID int64, err error) {
	return DefaultService.CreateFuseRule(ctx, name, desc, priority, detail, actions)
}

// UpdateFuseRuleByRuleID 更新规则记录
func UpdateFuseRuleByRuleID(ctx context.Context, ruleID int64, name, desc *string, priority *int64, status *fuseCommon.RuleStatus, detail *RuleDetail,
	actions *ActionDetail) (err error) {
	return DefaultService.UpdateFuseRuleByRuleID(ctx, ruleID, name, desc, priority, status, detail, actions)
}

// DeleteFuseRule 删除规则记录
func DeleteFuseRule(ctx context.Context, ruleIDs []int64) (err error) {
	return DefaultService.DeleteFuseRule(ctx, ruleIDs)
}

// QueryFuseRuleRelationBySeveralConditions 查询rule与资产ID绑定关系
func QueryFuseRuleRelationBySeveralConditions(ctx context.Context, filter QueryFuseRuleRelationFilter) ([]*RuleRelationTable, error) {
	return DefaultService.QueryFuseRuleRelationBySeveralConditions(ctx, filter)
}

// CreateFuseRuleRelations 创建rule与资产ID绑定关系
func CreateFuseRuleRelations(ctx context.Context, relations []*RuleRelationTable) ([]*RuleRelationTable, error) {
	return DefaultService.CreateFuseRuleRelations(ctx, relations)
}

// DeleteFuseRuleRelationsByRelationIDs 删除rule与资产ID绑定关系
func DeleteFuseRuleRelationsByRelationIDs(ctx context.Context, relationIDs []int64) (int64, error) {
	return DefaultService.DeleteFuseRuleRelationsByRelationIDs(ctx, relationIDs)
}

// DeleteFuseRuleRelationsByAssetTypeAndAssetIDs 根据资产ID列表，删除rule与资产ID绑定关系
func DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx context.Context,
	assetType fuseCommon.AssetType, assetIDs []int64) (rowsAffected int64, err error) {
	return DefaultService.DeleteFuseRuleRelationsByAssetTypeAndAssetIDs(ctx, assetType, assetIDs)
}

// DeleteFuseRuleRelationsByRuleIDs 根据规则ID列表，删除rule与资产ID绑定关系
func DeleteFuseRuleRelationsByRuleIDs(ctx context.Context, ruleIDs []int64) (rowsAffected int64, err error) {
	return DefaultService.DeleteFuseRuleRelationsByRuleIDs(ctx, ruleIDs)
}

// QueryRuleMaxUpdateTime 查询rule最大更新时间
func QueryRuleMaxUpdateTime(ctx context.Context) (time.Time, error) {
	return DefaultService.QueryRuleMaxUpdateTime(ctx)
}

// QueryRuleRelationMaxUpdateTime 查询rule绑定关系最大更新时间
func QueryRuleRelationMaxUpdateTime(ctx context.Context) (time.Time, error) {
	return DefaultService.QueryRuleRelationMaxUpdateTime(ctx)
}

// QueryRuleRelationTotalCount 查询rule绑定关系总数
func QueryRuleRelationTotalCount(ctx context.Context) (int64, error) {
	return DefaultService.QueryRuleRelationTotalCount(ctx)
}
