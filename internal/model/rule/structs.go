/**
 * @note
 * structs
 *
 * <AUTHOR>
 * @date 	2025-03-31
 */
package rule

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"math/big"
	"reflect"
	"strconv"
	"time"

	"gorm.io/gorm"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type RuleTable struct {
	gorm.Model
	Name     string                `gorm:"column:name"`     // 规则名
	Desc     string                `gorm:"column:desc"`     // 规则描述
	Priority int64                 `gorm:"column:priority"` // 优先级
	Status   fuseCommon.RuleStatus `gorm:"column:status"`   // 规则状态
	Detail   *RuleDetail           `gorm:"column:detail"`   // 规则内容
	Actions  *ActionDetail         `gorm:"column:actions"`  // 规则内容
}

type RuleDetail struct {
	SubRules []*SubRule `json:"subRules" validate:"required"`
}

func (rd *RuleDetail) Validate() error {
	for _, r := range rd.SubRules {
		if err := r.<PERSON>te(); err != nil {
			return err
		}
	}
	return nil
}

type ActionDetail struct {
	Message   bool `json:"message"`
	Phone     bool `json:"phone"`
	FuseToken bool `json:"fuseToken"`
	FuseChain bool `json:"fuseChain"`
}

type SubRule struct {
	Conditions []*Condition `json:"conditions"`
}

func (sb *SubRule) Validate() error {
	for _, condition := range sb.Conditions {
		if err := condition.Validate(); err != nil {
			return err
		}
	}
	return nil
}

type Condition struct {
	Param string      `json:"param"`
	Op    string      `json:"op"`
	Value interface{} `json:"value"`
}

func (c *Condition) Validate() error {
	/* 传入的param必须在
	ParamLatency
	ParamAmountDifference
	ParamTransactionType
	三个之中，并且当是ParamLatency时，op必须是>,<中的其中一个，并且值必须是一个数字值
	是ParamAmountDifference时，op必须是 >,<中的一个。并且，value如果是string类型，要校验是否是一个合法的big.Float，如果是一个数字值，则需要转换成string设置入value中
	是ParamTransactionType时，op必须是in，notin或==，当为==时，value必须是0或者1，当为in或者notin时，value必须是一个数组，且数组内不能包含0和1之外的数字
	*/

	// 检查参数是否在允许的范围内
	if c.Param != fuseCommon.ParamLatency &&
		c.Param != fuseCommon.ParamAmountDifference &&
		c.Param != fuseCommon.ParamTransactionType {
		return fmt.Errorf("invalid param name: %v, must be one of [%s, %s, %s]",
			c.Param, fuseCommon.ParamLatency, fuseCommon.ParamAmountDifference, fuseCommon.ParamTransactionType)
	}

	// 根据不同参数类型进行验证
	switch c.Param {
	case fuseCommon.ParamLatency:
		// latency参数，op必须是>或<，value必须是数字
		if c.Op != ">" && c.Op != "<" {
			return fmt.Errorf("invalid op: %v, must be one of [%s, %s]", c.Op, ">", "<")
		}

		// 验证value是否为数字
		switch v := c.Value.(type) {
		case float64, int, int64, float32:
			// 数字类型，直接通过
			return nil
		case string:
			// 字符串类型，尝试转换为数字
			f, err := strconv.ParseFloat(v, 64)
			if err != nil {
				return fmt.Errorf("invalid value for ParamLatency, must be a number: %v", err)
			}
			c.Value = f
		default:
			return fmt.Errorf("invalid value type for ParamLatency: %T, must be a number", c.Value)
		}

	case fuseCommon.ParamAmountDifference:
		// amountDifference参数，op必须是>或<
		if c.Op != ">" && c.Op != "<" {
			return fmt.Errorf("invalid op for ParamAmountDifference: %s, must be one of [>, <]", c.Op)
		}

		// 验证value是否为合法的big.Float
		var valueStr string

		switch v := c.Value.(type) {
		case string:
			valueStr = v
		case float64, int, int64, float32:
			// 如果是数字类型，转换为字符串
			valueStr = fmt.Sprintf("%v", v)
			// 将转换后的字符串设置回value
			c.Value = valueStr
		default:
			return fmt.Errorf("invalid value type for ParamAmountDifference: %T, must be a number or string", c.Value)
		}

		// 验证是否是合法的big.Float
		_, ok := new(big.Float).SetString(valueStr)
		if !ok {
			return fmt.Errorf("invalid value for ParamAmountDifference, must be a valid number: %s", valueStr)
		}

	case fuseCommon.ParamTransactionType:
		// transactionType参数，op必须是in、notIn或==
		if c.Op != fuseCommon.OpIn && c.Op != fuseCommon.OpNotIn && c.Op != "==" {
			return fmt.Errorf("invalid op for ParamTransactionType: %s, must be one of [%s, %s, ==]",
				c.Op, fuseCommon.OpIn, fuseCommon.OpNotIn)
		}

		// 当op是==时，value必须是0或1
		if c.Op == "==" {
			switch v := c.Value.(type) {
			case float64:
				if v != 0 && v != 1 {
					return fmt.Errorf("invalid value for ParamTransactionType with == op: %v, must be 0 or 1", v)
				}
			case int, int64:
				intVal := reflect.ValueOf(v).Int()
				if intVal != 0 && intVal != 1 {
					return fmt.Errorf("invalid value for ParamTransactionType with == op: %v, must be 0 or 1", v)
				}
			default:
				return fmt.Errorf("invalid value type for ParamTransactionType with == op: %T, must be a number", c.Value)
			}
		} else {
			// 当op是in或notIn时，value必须是数组，且只包含0和1
			valueArr, ok := c.Value.([]interface{})
			if !ok {
				return fmt.Errorf("invalid value type for ParamTransactionType with %s op: %T, must be an array", c.Op, c.Value)
			}

			if len(valueArr) == 0 {
				return fmt.Errorf("empty array is not allowed for ParamTransactionType with %s op", c.Op)
			}

			for i, item := range valueArr {
				switch v := item.(type) {
				case float64:
					if v != 0 && v != 1 {
						return fmt.Errorf("invalid value at index %d for ParamTransactionType: %v, must be 0 or 1", i, v)
					}
				case int, int64:
					intVal := reflect.ValueOf(v).Int()
					if intVal != 0 && intVal != 1 {
						return fmt.Errorf("invalid value at index %d for ParamTransactionType: %v, must be 0 or 1", i, v)
					}
				default:
					return fmt.Errorf("invalid value type at index %d for ParamTransactionType: %T, must be a number", i, item)
				}
			}
		}
	}

	return nil
}

func (t *RuleTable) TableName() string {
	return fuseCommon.FuseRuleTableName
}

type RuleRelationTable struct {
	ID        uint                 `gorm:"primarykey"`
	CreatedAt time.Time            `gorm:"column:created_at"`
	UpdatedAt time.Time            `gorm:"column:updated_at"`
	RuleID    int64                `gorm:"column:rule_id"`    // 规则的主键ID
	AssetType fuseCommon.AssetType `gorm:"column:asset_type"` // 绑定的资产是什么，是chain还是token
	AssetID   int64                `gorm:"column:asset_id"`   // 资产记录的主键ID，可能是链的主键ID也可能是token-chain的主键ID
}

func (t *RuleRelationTable) TableName() string {
	return fuseCommon.FuseRuleRelationTableName
}

func (r *RuleDetail) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, r)
}

func (r *RuleDetail) Value() (driver.Value, error) {
	return json.Marshal(r)
}

func (a *ActionDetail) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, a)
}

func (a *ActionDetail) Value() (driver.Value, error) {
	return json.Marshal(a)
}

type QueryFuseRuleFilter struct {
	Page, PerPage int
	SelectFields  []string
	Order         string
	RuleIDs       []int64
	Status        *fuseCommon.RuleStatus
}

type QueryFuseRuleRelationFilter struct {
	RuleIDs   []int64
	AssetType fuseCommon.AssetType
	AssetIDs  []int64
}
