/**
 * @note
 * trans
 *
 * <AUTHOR>
 * @date 	2025-04-01
 */
package rule

import (
	"context"
	"fmt"
	"strings"

	. "gitlab.docsl.com/security/common"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func (c *SubRule) Transfer(ctx context.Context, valueIdx *int64, valueMap map[string]interface{}) (trueStr, skipStr string, err error) {
	var conditionStrs, skipConditionStrs []string
	for _, condition := range c.Conditions {
		conditionStr, skipConditionStr, err := condition.transfer(ctx, valueIdx, valueMap)
		if err != nil {
			return StringEmpty, StringEmpty, err
		}
		if conditionStr != StringEmpty {
			conditionStrs = append(conditionStrs, conditionStr)
		}
		if skipConditionStr != StringEmpty {
			skipConditionStrs = append(skipConditionStrs, skipConditionStr)
		}
	}
	if len(conditionStrs) == 0 {
		return StringEmpty, StringEmpty, nil
	} else if len(conditionStrs) == 1 {
		trueStr = conditionStrs[0]
	} else {
		trueStr = "(" + strings.Join(conditionStrs, ") && (") + ")"
	}
	if len(skipConditionStrs) == 0 {
		skipStr = StringEmpty
	} else if len(skipConditionStrs) == 1 {
		skipStr = skipConditionStrs[0]
	} else {
		skipStr = "(" + strings.Join(skipConditionStrs, ") && (") + ")"
	}
	return trueStr, skipStr, nil
}

func (c *Condition) transfer(ctx context.Context, valueIdx *int64, valueMap map[string]interface{}) (trueStr, skipStr string, err error) {
	// amount特殊处理
	if c.Param == fuseCommon.ParamAmountDifference {
		return fmt.Sprintf(`bigFloatCmp(%s, "%s", "%s")`, c.Param, c.Op, c.Value), StringEmpty, nil
	}
	var valueName string
	switch c.Value.(type) {
	// 基础类型直接放代码里
	case bool, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		valueName = fmt.Sprintf("%d", c.Value)
	case string:
		valueName = fmt.Sprintf(`"%s"`, c.Value)
	default: // 把value放进valueMap用于计算
		valueName = fmt.Sprintf(`value_%d`, *valueIdx)
		*valueIdx++
		valueMap[valueName] = c.Value
	}
	switch c.Op {
	case "==", ">", "<":
		trueStr, skipStr = fmt.Sprintf(`%s %s %s`, c.Param, c.Op, valueName),
			fmt.Sprintf(`!(%s %s %s)`, c.Param, c.Op, valueName)
	case fuseCommon.OpIn:
		trueStr, skipStr = fmt.Sprintf(`in(%s, %s)`, c.Param, valueName),
			fmt.Sprintf(`!in(%s, %s)`, c.Param, valueName)
	case fuseCommon.OpNotIn:
		trueStr, skipStr = fmt.Sprintf(`!in(%s, %s)`, c.Param, valueName),
			fmt.Sprintf(`in(%s, %s)`, c.Param, valueName)
	default:
		err = fmt.Errorf("unknown operator: %s", c.Op)
	}
	if err != nil {
		return StringEmpty, StringEmpty, err
	}
	if c.Param == fuseCommon.ParamLatency && c.Op == ">" { // 这个latency由于是个时间变量，需要特殊处理
		return trueStr, skipStr, nil
	} else {
		return trueStr, StringEmpty, nil
	}
}
