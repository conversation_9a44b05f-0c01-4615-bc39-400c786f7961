/**
 * @note
 * order
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package transaction

import (
	"context"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryWithdrawOrdersBySeveralConditions 根据多个条件查询提现订单
func (m *TransactionModelImpl) QueryWithdrawOrdersBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) ([]*WithdrawOrderTable, error) {
	db = db.Model(&WithdrawOrderTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryOrderConditions(ctx, filter, db)

	tbs := make([]*WithdrawOrderTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

// CreateOrUpdateWithdrawOrders 插入或更新提现订单
func (m *TransactionModelImpl) CreateOrUpdateWithdrawOrders(ctx context.Context, db *gorm.DB,
	orders []*WithdrawOrderTable) (err error) {
	t := &WithdrawOrderTable{}
	db = db.Model(t)

	// 生成ID
	for _, order := range orders {
		order.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "order_id"}},           // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(orders, 10).Error
	return err
}

// UpdateWithdrawOrderByOrderID 更新提现订单记录
func (m *TransactionModelImpl) UpdateWithdrawOrderByOrderID(ctx context.Context, db *gorm.DB, orderID int64,
	validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error) {
	db = db.Model(&WithdrawOrderTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	if transactionUnitID != nil {
		updateMap["transaction_unit_id"] = *transactionUnitID
	}
	return db.Where("order_id = ?", orderID).Updates(updateMap).Error
}

// UpdateWithdrawOrderByUnitIDAndState 根据UnitID更新提现订单记录
func (m *TransactionModelImpl) UpdateWithdrawOrderByUnitIDAndState(ctx context.Context, db *gorm.DB, unitID int64,
	orderState fuseCommon.WithdrawOrderState, validateState *fuseCommon.ValidateState) (err error) {
	if unitID == 0 {
		return ErrMissingAMandatoryParameterf("unit_id")
	}
	db = db.Model(&WithdrawOrderTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	return db.Where("transaction_unit_id = ? and state = ?", unitID, orderState).Updates(updateMap).Error
}

// QueryDepositOrdersBySeveralConditions 根据多个条件查询充值订单
func (m *TransactionModelImpl) QueryDepositOrdersBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) ([]*DepositOrderTable, error) {
	db = db.Model(&DepositOrderTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryOrderConditions(ctx, filter, db)

	tbs := make([]*DepositOrderTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

// CreateOrUpdateDepositOrders 插入或更新充值订单
func (m *TransactionModelImpl) CreateOrUpdateDepositOrders(ctx context.Context, db *gorm.DB,
	orders []*DepositOrderTable) (err error) {
	t := &DepositOrderTable{}
	db = db.Model(t)

	// 生成ID
	for _, order := range orders {
		order.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "order_id"}},           // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(orders, 10).Error
	return err
}

// UpdateDepositOrderByOrderID 更新充值订单记录
func (m *TransactionModelImpl) UpdateDepositOrderByOrderID(ctx context.Context, db *gorm.DB, orderID int64,
	validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error) {
	db = db.Model(&DepositOrderTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	if transactionUnitID != nil {
		updateMap["transaction_unit_id"] = *transactionUnitID
	}
	return db.Where("order_id = ?", orderID).Updates(updateMap).Error
}

// UpdateDepositOrderByUnitIDAndState 根据UnitID更新充值订单记录
func (m *TransactionModelImpl) UpdateDepositOrderByUnitIDAndState(ctx context.Context, db *gorm.DB, unitID int64,
	orderState fuseCommon.DepositOrderState, validateState *fuseCommon.ValidateState) (err error) {
	if unitID == 0 {
		return ErrMissingAMandatoryParameterf("unit_id")
	}
	db = db.Model(&DepositOrderTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	return db.Where("transaction_unit_id = ? and state = ?", unitID, orderState).Updates(updateMap).Error
}

func assembleQueryOrderConditions(ctx context.Context, filter QueryOrderFilter, db *gorm.DB) *gorm.DB {
	// orderID筛选
	if len(filter.OrderIDs) > 1 {
		db = db.Where("order_id in (?)", filter.OrderIDs)
	} else if len(filter.OrderIDs) == 1 && filter.OrderIDs[0] > 0 {
		db = db.Where("order_id = ?", filter.OrderIDs[0])
	}
	// 链ID
	if filter.ChainID != StringEmpty {
		db = db.Where("chain_id = ?", filter.ChainID)
	}
	// 币种ID
	if filter.TokenID != StringEmpty {
		db = db.Where("token_id = ?", filter.TokenID)
	}
	// 交易Hash
	if filter.TransactionHash != StringEmpty {
		db = db.Where("transaction_hash = ?", filter.TransactionHash)

	}
	// 交易Index
	if filter.TransferIndex != nil {
		db = db.Where("transfer_index = ?", filter.TransferIndex)
	}
	// ToAddress
	if filter.ToAddress != StringEmpty {
		db = db.Where("to_address = ?", filter.ToAddress)
	}

	// FormattedToAddress
	if filter.FormattedToAddress != StringEmpty {
		db = db.Where("formatted_to_address = ?", filter.FormattedToAddress)
	}

	// memo
	if filter.Memo != StringEmpty {
		db = db.Where("memo = ?", filter.Memo)
	}

	// transaction_unit
	if len(filter.TransactionUnitIDs) > 1 {
		db = db.Where("transaction_unit_id in (?)", filter.TransactionUnitIDs)
	} else if len(filter.TransactionUnitIDs) == 1 {
		db = db.Where("transaction_unit_id = ?", filter.TransactionUnitIDs[0])
	}

	// validate state
	if len(filter.ValidateStates) > 0 {
		db = db.Where("validate_state in (?)", filter.ValidateStates)
	}

	// 订单状态
	if len(filter.DepositStates) > 0 {
		db = db.Where("state in (?)", filter.DepositStates)
	} else if len(filter.WithdrawStates) > 0 {
		db = db.Where("state in (?)", filter.WithdrawStates)
	}
	return db
}

// QueryDepositOrderCountBySeveralConditions 查询充值订单总数
func (m *TransactionModelImpl) QueryDepositOrderCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) (count int64, err error) {
	db = db.Model(&DepositOrderTable{})

	db = assembleQueryOrderConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

// QueryWithdrawOrderCountBySeveralConditions 查询提现订单总数
func (m *TransactionModelImpl) QueryWithdrawOrderCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) (count int64, err error) {
	db = db.Model(&WithdrawOrderTable{})

	db = assembleQueryOrderConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *TransactionModelImpl) QueryOrderMaxUpdateTimeGroupByWalletID(ctx context.Context, db *gorm.DB) (map[string]time.Time, error) {
	db = db.Select("wallet_id, max(update_time) as max_update_time")
	db = db.Group("wallet_id")
	db = db.Order("max_update_time desc")
	var tbs []struct {
		WalletID      string    `gorm:"column:wallet_id"`
		MaxUpdateTime time.Time `gorm:"column:max_update_time"`
	}
	db = db.Find(&tbs)
	if db.Error != nil {
		return nil, db.Error
	}
	ret := make(map[string]time.Time)
	for _, tb := range tbs {
		ret[tb.WalletID] = tb.MaxUpdateTime
	}
	return ret, nil
}
