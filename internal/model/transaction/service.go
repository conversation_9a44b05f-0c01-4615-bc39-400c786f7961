/**
 * @note
 * service
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package transaction

import (
	"context"
	"slices"
	"time"

	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type TransactionModel interface {
	getDB(ctx context.Context) (db *gorm.DB, err error)

	// QueryWithdrawOrdersBySeveralConditions 根据多个条件查询提现订单
	QueryWithdrawOrdersBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) ([]*WithdrawOrderTable, error)
	// QueryWithdrawOrderCountBySeveralConditions 查询提现订单总数
	QueryWithdrawOrderCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) (int64, error)
	// CreateOrUpdateWithdrawOrders 插入或更新提现订单
	CreateOrUpdateWithdrawOrders(ctx context.Context, db *gorm.DB, orders []*WithdrawOrderTable) error
	// UpdateWithdrawOrderByOrderID 更新提现订单记录
	UpdateWithdrawOrderByOrderID(ctx context.Context, db *gorm.DB, orderID int64, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error)
	// UpdateWithdrawOrderByUnitIDAndState 根据unitID更新提现订单记录
	UpdateWithdrawOrderByUnitIDAndState(ctx context.Context, db *gorm.DB, unitID int64, orderState fuseCommon.WithdrawOrderState, validateState *fuseCommon.ValidateState) (err error)

	// QueryDepositOrdersBySeveralConditions 根据多个条件查询充值订单
	QueryDepositOrdersBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) ([]*DepositOrderTable, error)
	// QueryDepositOrderCountBySeveralConditions 查询充值订单总数
	QueryDepositOrderCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryOrderFilter) (int64, error)
	// CreateOrUpdateDepositOrders 插入或更新充值订单
	CreateOrUpdateDepositOrders(ctx context.Context, db *gorm.DB, orders []*DepositOrderTable) error
	// UpdateDepositOrderByOrderID 更新充值订单记录
	UpdateDepositOrderByOrderID(ctx context.Context, db *gorm.DB, orderID int64, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error)
	// UpdateDepositOrderByUnitIDAndState 根据unitID和订单state更新充值订单记录
	UpdateDepositOrderByUnitIDAndState(ctx context.Context, db *gorm.DB, unitID int64, orderState fuseCommon.DepositOrderState, validateState *fuseCommon.ValidateState) (err error)
	// QueryOrderMaxUpdateTimeGroupByWalletID 查询不同walletID下的最大更新时间
	QueryOrderMaxUpdateTimeGroupByWalletID(ctx context.Context, db *gorm.DB) (map[string]time.Time, error)

	// QueryStatementsBySeveralConditions 根据多个条件查询钱包流水
	QueryStatementsBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryStatementFilter) ([]*StatementTable, error)
	// QueryStatementCountBySeveralConditions 查询流水总数
	QueryStatementCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryStatementFilter) (int64, error)
	// CreateOrUpdateStatements 插入或更新钱包流水
	CreateOrUpdateStatements(ctx context.Context, db *gorm.DB, orders []*StatementTable) error
	// UpdateStatementByStatementIDAndWalletIDAndChainID 更新钱包流水记录
	UpdateStatementByStatementIDAndWalletIDAndChainID(ctx context.Context, db *gorm.DB, statementID,
		walletID, chainID string, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error)
	// UpdateStatementByUnitID 根据unitID更新钱包流水记录
	UpdateStatementByUnitID(ctx context.Context, db *gorm.DB, unitID int64, validateState *fuseCommon.ValidateState) (err error)
	// QueryStatementMaxTimestampGroupByWalletIDAndChainID 查出不同walletID和ChainID下的最大流水时间
	QueryStatementMaxTimestampGroupByWalletIDAndChainID(ctx context.Context, db *gorm.DB) (map[string]map[string]int64, error)

	// QueryTransactionUnitsBySeveralConditions 根据多个条件查询订单+流水单元
	QueryTransactionUnitsBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryTransactionUnitFilter) ([]*TransactionUnitTable, error)
	// QueryTransactionUnitCountBySeveralConditions 根据多个条件查询Unit总量
	QueryTransactionUnitCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryTransactionUnitFilter) (int64, error)
	// CreateTransactionUnit 插入订单+流水单元
	CreateTransactionUnit(ctx context.Context, db *gorm.DB, transactionUnit *TransactionUnitTable) (int64, error)
	// UpdateTransactionUnitByID 更新订单+流水单元
	UpdateTransactionUnitByID(ctx context.Context, db *gorm.DB, id int64, validateState *fuseCommon.ValidateState, hitRuleID *int64, walletID *string,
		statementIDs *StatementIDsArray, orderIDs *OrderIDsArray) (err error)
}

type TransactionModelImpl struct{}

func (m *TransactionModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

var DefaultService TransactionModel = &TransactionModelImpl{}

// QueryWithdrawOrdersBySeveralConditions 根据多个条件查询提现订单
func QueryWithdrawOrdersBySeveralConditions(ctx context.Context, filter QueryOrderFilter) ([]*WithdrawOrderTable, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryWithdrawOrdersBySeveralConditions(ctx, db, filter)
}

// QueryWithdrawOrderCountBySeveralConditions 根据多个条件查询提现订单
func QueryWithdrawOrderCountBySeveralConditions(ctx context.Context, filter QueryOrderFilter) (int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryWithdrawOrderCountBySeveralConditions(ctx, db, filter)
}

// CreateOrUpdateWithdrawOrders 插入或更新提现订单
func CreateOrUpdateWithdrawOrders(ctx context.Context, orders []*WithdrawOrderTable) error {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.CreateOrUpdateWithdrawOrders(ctx, db, orders)
}

// UpdateWithdrawOrderByOrderID 更新提现订单记录
func UpdateWithdrawOrderByOrderID(ctx context.Context, orderID int64, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.UpdateWithdrawOrderByOrderID(ctx, db, orderID, validateState, transactionUnitID)
}

// QueryDepositOrdersBySeveralConditions 根据多个条件查询充值订单
func QueryDepositOrdersBySeveralConditions(ctx context.Context, filter QueryOrderFilter) ([]*DepositOrderTable, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryDepositOrdersBySeveralConditions(ctx, db, filter)
}

// QueryDepositOrderCountBySeveralConditions 根据多个条件查询充值订单数量
func QueryDepositOrderCountBySeveralConditions(ctx context.Context, filter QueryOrderFilter) (int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryDepositOrderCountBySeveralConditions(ctx, db, filter)
}

// CreateOrUpdateDepositOrders 插入或更新充值订单
func CreateOrUpdateDepositOrders(ctx context.Context, orders []*DepositOrderTable) error {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.CreateOrUpdateDepositOrders(ctx, db, orders)
}

// UpdateDepositOrderByOrderID 更新充值订单记录
func UpdateDepositOrderByOrderID(ctx context.Context, orderID int64, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.UpdateDepositOrderByOrderID(ctx, db, orderID, validateState, transactionUnitID)
}

// QueryStatementsBySeveralConditions 根据多个条件查询钱包流水
func QueryStatementsBySeveralConditions(ctx context.Context, filter QueryStatementFilter) ([]*StatementTable, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryStatementsBySeveralConditions(ctx, db, filter)
}

// QueryStatementCountBySeveralConditions 根据多个条件查询钱包流水数量
func QueryStatementCountBySeveralConditions(ctx context.Context, filter QueryStatementFilter) (int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryStatementCountBySeveralConditions(ctx, db, filter)
}

// CreateOrUpdateStatements 插入或更新钱包流水
func CreateOrUpdateStatements(ctx context.Context, orders []*StatementTable) error {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.CreateOrUpdateStatements(ctx, db, orders)
}

func QueryStatementMaxTimestampGroupByWalletIDAndChainID(ctx context.Context) (map[string]map[string]int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryStatementMaxTimestampGroupByWalletIDAndChainID(ctx, db)
}

func (UpdateStatementByStatementIDAndWalletIDAndChainID(ctx context.Context, db *gorm.DB, statementID,
walletID, chainID string, validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error){
	
}

func QueryDepositOrderMaxUpdateTimeGroupByWalletID(ctx context.Context) (map[string]time.Time, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&DepositOrderTable{})
	return DefaultService.QueryOrderMaxUpdateTimeGroupByWalletID(ctx, db)
}

func QueryWithdrawOrderMaxUpdateTimeGroupByWalletID(ctx context.Context) (map[string]time.Time, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&WithdrawOrderTable{})
	return DefaultService.QueryOrderMaxUpdateTimeGroupByWalletID(ctx, db)
}

// QueryTransactionUnitsBySeveralConditions 根据多个条件查询订单+流水单元
func QueryTransactionUnitsBySeveralConditions(ctx context.Context, filter QueryTransactionUnitFilter) ([]*TransactionUnitTable, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryTransactionUnitsBySeveralConditions(ctx, db, filter)
}

// QueryTransactionUnitCountBySeveralConditions 根据多个条件查询Unit总量
func QueryTransactionUnitCountBySeveralConditions(ctx context.Context, filter QueryTransactionUnitFilter) (int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryTransactionUnitCountBySeveralConditions(ctx, db, filter)
}

// CreateTransactionUnit 插入订单+流水单元
func CreateTransactionUnit(ctx context.Context, transactionUnit *TransactionUnitTable) (int64, error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.CreateTransactionUnit(ctx, db, transactionUnit)
}

// UpdateWithdrawTransactionUnitByID 更新提现订单+流水单元
func UpdateWithdrawTransactionUnitByID(ctx context.Context, id int64, validateState *fuseCommon.ValidateState, hitRuleID *int64,
	walletID *string, statementIDs *StatementIDsArray, orderIDs *OrderIDsArray) (err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	if validateState == nil {
		return DefaultService.UpdateTransactionUnitByID(ctx, db, id, validateState, hitRuleID, walletID, statementIDs, orderIDs)
	}
	// 如果是更新validate state的情况，开启事务，把订单，流水，unit的状态全更新
	tx := db.Begin()
	if err = DefaultService.UpdateTransactionUnitByID(ctx, tx, id, validateState, hitRuleID, walletID, statementIDs, orderIDs); err != nil {
		tx.Rollback()
		return err
	} else if err = DefaultService.UpdateWithdrawOrderByUnitIDAndState(ctx, tx, id, fuseCommon.WithdrawOrderStateSuccess, validateState); err != nil {
		tx.Rollback() // 2025-05-29 只改成功订单的校验状态
		return err
	} else if err = DefaultService.UpdateStatementByUnitID(ctx, tx, id, validateState); err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// UpdateDepositTransactionUnitByID 更新充值订单+流水单元
func UpdateDepositTransactionUnitByID(ctx context.Context, id int64, validateState *fuseCommon.ValidateState, hitRuleID *int64,
	walletID *string, statementIDs *StatementIDsArray, orderIDs *OrderIDsArray) (err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	if validateState == nil {
		return DefaultService.UpdateTransactionUnitByID(ctx, db, id, validateState, hitRuleID, walletID, statementIDs, orderIDs)
	}
	// 如果是更新validate state的情况，开启事务，把订单，流水，unit的状态全更新
	tx := db.Begin()
	if err = DefaultService.UpdateTransactionUnitByID(ctx, tx, id, validateState, hitRuleID, walletID, statementIDs, orderIDs); err != nil {
		tx.Rollback()
		return err
	} else if err = DefaultService.UpdateDepositOrderByUnitIDAndState(ctx, tx, id, fuseCommon.DepositOrderStateSuccess, validateState); err != nil {
		tx.Rollback() // 2025-05-29 只改成功订单的校验状态
		return err
	} else if err = DefaultService.UpdateStatementByUnitID(ctx, tx, id, validateState); err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// CreateOrUpdateTransactionUnitByDepositOrder 根据充值订单创建unit
func CreateOrUpdateTransactionUnitByDepositOrder(ctx context.Context, order *DepositOrderTable) (unitID int64, err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	// 开启事务
	tx := db.Begin()
	t := fuseCommon.TransactionTypeDeposit
	state := fuseCommon.ValidateStateToBeValidated
	// 查询已有unit
	existUnits, err := DefaultService.QueryTransactionUnitsBySeveralConditions(ctx, tx, QueryTransactionUnitFilter{
		ChainID:         order.ChainID,
		TokenID:         order.TokenID,
		TransactionHash: order.TransactionHash,
		TransferIndex:   &order.TransferIndex,
		ToAddress:       order.ToAddress,
		Memo:            order.Memo,
		Type:            &t,
	})
	if err != nil {
		tx.Rollback()
		return 0, err
	}
	if len(existUnits) > 1 {
		tx.Rollback()
		return 0, fuseCommon.ErrTransactionUnitNotUnique
	}
	// 如果已存在对应的unit
	if len(existUnits) == 1 {
		unitID = int64(existUnits[0].ID)
		if !slices.Contains(existUnits[0].OrderIDs, order.OrderID) { // orderID不存在unit里，将orderID添加入这个unit中（当前钱包系统设计不存在这种可能）
			existUnits[0].OrderIDs = append(existUnits[0].OrderIDs, order.OrderID)
			err = DefaultService.UpdateTransactionUnitByID(ctx, tx, unitID, &state, nil, nil, nil, &existUnits[0].OrderIDs) // 更新orderID数组
		} else { // orderID已经存在在unit里了，在充值回滚的情况下可能会发生
			err = DefaultService.UpdateTransactionUnitByID(ctx, tx, unitID, &state, nil, nil, nil, nil) // 仅更新状态
		}
		if err != nil {
			tx.Rollback()
			return 0, err
		}
	} else {
		// 创建新unit
		transactionUnit := &TransactionUnitTable{
			Type:            fuseCommon.TransactionTypeDeposit,
			OrderIDs:        OrderIDsArray{order.OrderID},
			StatementIDs:    StatementIDsArray{},
			ValidateState:   fuseCommon.ValidateStateToBeValidated,
			WalletID:        order.WalletID,
			ChainID:         order.ChainID,
			TokenID:         order.TokenID,
			TransactionHash: order.TransactionHash,
			TransferIndex:   order.TransferIndex,
			ToAddress:       order.ToAddress,
			Memo:            order.Memo,
		}
		unitID, err = DefaultService.CreateTransactionUnit(ctx, tx, transactionUnit)
		if err != nil {
			tx.Rollback()
			return 0, err
		}
	}
	// 更新order信息
	err = DefaultService.UpdateDepositOrderByOrderID(ctx, tx, order.OrderID, &state, &unitID)
	if err != nil {
		tx.Rollback()
		return 0, err
	}
	// 提交
	return unitID, tx.Commit().Error
}

// CreateOrUpdateTransactionUnitByStatements 根据提现流水创建unit，包括回滚和再打包流水
func CreateOrUpdateTransactionUnitByStatements(ctx context.Context,
	transactionType fuseCommon.TransactionType, rootStatement *StatementTable, childStatements ...*StatementTable) (unitID int64, err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return 0, err
	}
	// 开启事务
	tx := db.Begin()
	state := fuseCommon.ValidateStateToBeValidated
	// 根据rootStatement查询已有unit
	existUnits, err := DefaultService.QueryTransactionUnitsBySeveralConditions(ctx, tx, func() QueryTransactionUnitFilter {
		if rootStatement.TransactionUnitID != 0 {
			return QueryTransactionUnitFilter{
				IDs:  []int64{rootStatement.TransactionUnitID},
				Type: &transactionType,
			}
		}
		return QueryTransactionUnitFilter{
			ChainID:         rootStatement.ChainID,
			TokenID:         rootStatement.TokenID,
			TransactionHash: rootStatement.TransactionHash,
			TransferIndex:   &rootStatement.TransferIndex,
			ToAddress:       rootStatement.ToAddress,
			Memo:            rootStatement.Memo,
			Type:            &transactionType,
		}
	}())
	if err != nil {
		tx.Rollback()
		return 0, err
	}
	if len(existUnits) > 1 {
		tx.Rollback()
		return 0, fuseCommon.ErrTransactionUnitNotUnique
	}
	// 如果已存在对应的unit
	allStatementIDs := StatementIDsArray{rootStatement.StatementID}
	for _, childStatement := range childStatements {
		allStatementIDs = append(allStatementIDs, childStatement.StatementID)
	}
	if len(existUnits) == 1 {
		unitID = int64(existUnits[0].ID)
		for _, statementID := range allStatementIDs {
			if !slices.Contains(existUnits[0].StatementIDs, statementID) { // statementID不存在unit里，将statementID添加入这个unit中
				existUnits[0].StatementIDs = append(existUnits[0].StatementIDs, statementID)
			}
		}
		err = DefaultService.UpdateTransactionUnitByID(ctx, tx, unitID, &state, nil, nil, &existUnits[0].StatementIDs, nil) // 更新statementID数组
		if err != nil {
			tx.Rollback()
			return 0, err
		}
	} else {
		// 创建新unit
		transactionUnit := &TransactionUnitTable{
			Type:            transactionType,
			OrderIDs:        OrderIDsArray{},
			StatementIDs:    allStatementIDs,
			ValidateState:   fuseCommon.ValidateStateToBeValidated,
			WalletID:        rootStatement.WalletID,
			ChainID:         rootStatement.ChainID,
			TokenID:         rootStatement.TokenID,
			TransactionHash: rootStatement.TransactionHash,
			TransferIndex:   rootStatement.TransferIndex,
			ToAddress:       rootStatement.ToAddress,
			Memo:            rootStatement.Memo,
		}
		unitID, err = DefaultService.CreateTransactionUnit(ctx, tx, transactionUnit)
		if err != nil {
			tx.Rollback()
			return 0, err
		}
	}
	// 更新所有statement信息
	for _, statementID := range allStatementIDs {
		err = DefaultService.UpdateStatementByStatementIDAndWalletIDAndChainID(ctx, tx, statementID, rootStatement.WalletID, rootStatement.ChainID, &state, &unitID)
		if err != nil {
			tx.Rollback()
			return 0, err
		}
	}
	// 提交
	return unitID, tx.Commit().Error
}

// UpdateTransactionUnitAndStatementsAndOrdersAllAtOnce 事务性更新
// unitID 基础的unit的主键
// walletID walletID
// chainID 链ID
// unitStatementIDs 需要更新到transaction unit中的statementID数组
// unitOrderIDs 需要更新到transaction unit中的orderID数组
// unitValidateState 需要更新到transaction unit中的validate state
// needUpdateStatementIDs 需要更新状态或unitID的statementIDs
// needUpdateDepositOrderIDs 需要更新状态或unitID的充值orderIDs
// needUpdateWithdrawOrderIDs 需要更新状态或unitID的提现orderIDs
// statementOrderUnitID 需要更新到statement或order中的unitID
// statementOrderValidateState 需要更新到statement或order中的validate state
func UpdateTransactionUnitAndStatementsAndOrdersAllAtOnce(ctx context.Context, walletID, chainID string,
	unitID int64, unitStatementIDs *StatementIDsArray, unitOrderIDs *OrderIDsArray, unitValidateState *fuseCommon.ValidateState,
	needUpdateStatementIDs []string, needUpdateDepositOrderIDs []int64, needUpdateWithdrawOrderIDs []int64,
	statementOrderUnitID *int64, statementOrderValidateState *fuseCommon.ValidateState) (err error) {
	db, err := DefaultService.getDB(ctx)
	if err != nil {
		return err
	}
	// 开启事务
	tx := db.Begin()
	// 更新transactionUnit
	if unitStatementIDs != nil || unitOrderIDs != nil || unitValidateState != nil {
		err = DefaultService.UpdateTransactionUnitByID(ctx, tx, unitID, unitValidateState, nil, nil, unitStatementIDs, unitOrderIDs)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	if statementOrderUnitID != nil || statementOrderValidateState != nil {
		// 更新statement
		for _, statementID := range needUpdateStatementIDs {
			err = DefaultService.UpdateStatementByStatementIDAndWalletIDAndChainID(ctx, tx, statementID, walletID, chainID, statementOrderValidateState, statementOrderUnitID)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
		// 更新充值order
		for _, orderID := range needUpdateDepositOrderIDs {
			err = DefaultService.UpdateDepositOrderByOrderID(ctx, tx, orderID, statementOrderValidateState, statementOrderUnitID)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
		// 更新提现order
		for _, orderID := range needUpdateWithdrawOrderIDs {
			err = DefaultService.UpdateWithdrawOrderByOrderID(ctx, tx, orderID, statementOrderValidateState, statementOrderUnitID)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	// 提交事务
	return tx.Commit().Error
}
