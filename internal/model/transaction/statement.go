/**
 * @note
 * statement
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package transaction

import (
	"context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryStatementsBySeveralConditions 根据多个条件查询钱包流水
func (m *TransactionModelImpl) QueryStatementsBySeveralConditions(ctx context.Context, db *gorm.DB,
	filter QueryStatementFilter) ([]*StatementTable, error) {
	db = db.Model(&StatementTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryStatementConditions(ctx, filter, db)

	tbs := make([]*StatementTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

// CreateOrUpdateStatements 插入或更新钱包流水
func (m *TransactionModelImpl) CreateOrUpdateStatements(ctx context.Context, db *gorm.DB,
	orders []*StatementTable) (err error) {
	t := &StatementTable{}
	db = db.Model(t)

	// 生成ID
	for _, order := range orders {
		order.ID = uint(idgen.GetID())
	}
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "statement_id"}, {Name: "wallet_id"}, {Name: "chain_id"}}, // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()),                                    // 冲突时更新这些字段
	}).CreateInBatches(orders, 10).Error
	return err
}

// UpdateStatementByStatementIDAndWalletIDAndChainID 更新钱包流水记录
func (m *TransactionModelImpl) UpdateStatementsByStatementIDsAndWalletIDAndChainID(ctx context.Context, db *gorm.DB,
	statementIDs []string, walletID, chainID string,
	validateState *fuseCommon.ValidateState, transactionUnitID *int64) (err error) {
	db = db.Model(&StatementTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	if transactionUnitID != nil {
		updateMap["transaction_unit_id"] = *transactionUnitID
	}
	return db.Where("statement_id in (?) and wallet_id = ? and chain_id = ?", statementIDs, walletID, chainID).Updates(updateMap).Error
}

// UpdateStatementByUnitID 根据UnitID更新钱包流水记录
func (m *TransactionModelImpl) UpdateStatementByUnitID(ctx context.Context, db *gorm.DB, unitID int64,
	validateState *fuseCommon.ValidateState) (err error) {
	if unitID == 0 {
		return ErrMissingAMandatoryParameterf("unit_id")
	}
	db = db.Model(&StatementTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	return db.Where("transaction_unit_id = ?", unitID).Updates(updateMap).Error
}

func assembleQueryStatementConditions(ctx context.Context, filter QueryStatementFilter, db *gorm.DB) *gorm.DB {
	// statementID筛选
	if len(filter.StatementIDs) > 1 {
		db = db.Where("statement_id in (?)", filter.StatementIDs)
	} else if len(filter.StatementIDs) == 1 && filter.StatementIDs[0] != StringEmpty {
		db = db.Where("statement_id = ?", filter.StatementIDs[0])
	}
	// referID筛选
	if len(filter.ReferIDs) > 1 {
		db = db.Where("refer_id in (?)", filter.ReferIDs)
	} else if len(filter.ReferIDs) == 1 && filter.ReferIDs[0] != StringEmpty {
		db = db.Where("refer_id = ?", filter.ReferIDs[0])
	}
	// WalletID
	if filter.WalletID != StringEmpty {
		db = db.Where("wallet_id = ?", filter.WalletID)
	}
	// 链ID
	if filter.ChainID != StringEmpty {
		db = db.Where("chain_id = ?", filter.ChainID)
	}
	// 币种ID
	if filter.TokenID != StringEmpty {
		db = db.Where("token_id = ?", filter.TokenID)
	}
	// 交易Hash
	if filter.TransactionHash != StringEmpty {
		db = db.Where("transaction_hash = ?", filter.TransactionHash)

	}
	// 交易Index
	if filter.TransferIndex != nil {
		db = db.Where("transfer_index = ?", filter.TransferIndex)
	}
	// ToAddress
	if filter.ToAddress != StringEmpty {
		db = db.Where("to_address = ?", filter.ToAddress)
	}
	// ToAddress
	if filter.Memo != StringEmpty {
		db = db.Where("memo = ?", filter.Memo)
	}
	// 流水类型状态
	if filter.BusinessType != StringEmpty {
		db = db.Where("business_type = ?", filter.BusinessType)
	}

	// transaction_unit
	if len(filter.TransactionUnitIDs) > 1 {
		db = db.Where("transaction_unit_id in (?)", filter.TransactionUnitIDs)
	} else if len(filter.TransactionUnitIDs) == 1 {
		db = db.Where("transaction_unit_id = ?", filter.TransactionUnitIDs[0])
	}

	// 流水状态
	if filter.State != nil {
		db = db.Where("state = ?", filter.State)
	}

	// 校验状态
	if len(filter.ValidateStates) > 0 {
		db = db.Where("validate_state in (?)", filter.ValidateStates)
	}

	return db
}

// QueryStatementCountBySeveralConditions 查询流水总数
func (m *TransactionModelImpl) QueryStatementCountBySeveralConditions(ctx context.Context, db *gorm.DB, filter QueryStatementFilter) (count int64, err error) {
	db = db.Model(&StatementTable{})

	db = assembleQueryStatementConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *TransactionModelImpl) QueryStatementMaxTimestampGroupByWalletIDAndChainID(ctx context.Context, db *gorm.DB) (map[string]map[string]int64, error) {
	db = db.Model(&StatementTable{})
	db = db.Select("wallet_id, chain_id, max(created_timestamp) as max_timestamp")
	db = db.Group("wallet_id, chain_id")
	db = db.Order("max_timestamp desc")
	var tbs []struct {
		WalletID     string `gorm:"column:wallet_id"`
		ChainID      string `gorm:"column:chain_id"`
		MaxTimestamp int64  `gorm:"column:max_timestamp"`
	}
	db = db.Find(&tbs)
	if db.Error != nil {
		return nil, db.Error
	}
	ret := make(map[string]map[string]int64)
	for _, tb := range tbs {
		if ret[tb.WalletID] == nil {
			ret[tb.WalletID] = make(map[string]int64)
		}
		ret[tb.WalletID][tb.ChainID] = tb.MaxTimestamp
	}
	return ret, nil
}
