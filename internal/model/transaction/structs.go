package transaction

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type StatementTable struct {
	gorm.Model
	StatementID      string                           `gorm:"column:statement_id"` // 拉取到的流水ID
	ReferID          string                           `gorm:"column:refer_id"`     // 关联的上一条流水ID（发生回滚情况时不为空）
	BlockHeight      string                           `gorm:"column:block_height"`
	BlockHash        string                           `gorm:"column:block_hash"`
	TransactionHash  string                           `gorm:"column:transaction_hash"`
	TransferIndex    int64                            `gorm:"column:transfer_index"`
	BlockTimestamp   string                           `gorm:"column:block_timestamp"`
	FromAddress      string                           `gorm:"column:from_address"`
	ToAddress        string                           `gorm:"column:to_address"`
	WalletID         string                           `gorm:"column:wallet_id"`
	ChainID          string                           `gorm:"column:chain_id"`
	TokenID          string                           `gorm:"column:token_id"`
	Amount           string                           `gorm:"column:amount"`
	UserTypeBalance  string                           `gorm:"column:user_type_balance"`
	SysTypeBalance   string                           `gorm:"column:sys_type_balance"`
	ColdTypeBalance  string                           `gorm:"column:cold_type_balance"`
	PlatformBalance  string                           `gorm:"column:platform_balance"`
	Memo             string                           `gorm:"column:memo"`
	BusinessType     fuseCommon.StatementBusinessType `gorm:"column:business_type"`
	State            fuseCommon.StatementState        `gorm:"column:state"`
	CreatedTimestamp int64                            `gorm:"column:created_timestamp"`

	ValidateState     fuseCommon.ValidateState `gorm:"column:validate_state"`      // 标记校验状态
	TransactionUnitID int64                    `gorm:"column:transaction_unit_id"` // 关联的TransactionUnit
}

type DepositOrderTable struct {
	gorm.Model
	OrderID          int64                        `gorm:"column:order_id"`           // 订单id
	TenantID         int64                        `gorm:"column:tenant_id"`          // 租户id
	ClientID         int64                        `gorm:"column:client_id"`          // 机构id
	PortfolioID      int64                        `gorm:"column:portfolio_id"`       // 子账户id
	WalletOrderID    string                       `gorm:"column:wallet_order_id"`    // 钱包订单id
	WalletID         string                       `gorm:"column:wallet_id"`          // 钱包
	ChainID          string                       `gorm:"column:chain_id"`           // 链名称
	TokenID          string                       `gorm:"column:token_id"`           // 币种名称
	Currency         string                       `gorm:"column:currency"`           // 币种Code
	ToAddressID      int64                        `gorm:"column:to_address_id"`      // 目标地址
	ToAddress        string                       `gorm:"column:to_address"`         // 目标地址id
	Memo             string                       `gorm:"column:memo"`               // memo
	TransactionHash  string                       `gorm:"column:transaction_hash"`   // 交易hash
	TransferIndex    int64                        `gorm:"column:transfer_index"`     // 交易index
	Amount           string                       `gorm:"column:amount"`             // 充值金额
	StartTime        int64                        `gorm:"column:start_time"`         // 开始时间
	EndTime          int64                        `gorm:"column:end_time"`           // 结束时间
	State            fuseCommon.DepositOrderState `gorm:"column:state"`              // 充值订单状态 0-初始化；1-区块确认中；2-处理中；3-审核中；4-待认证；10-充值中；11-充值成功；20-充值失败中；21-充值失败；30-充值异常；31-充值回滚
	KytState         int64                        `gorm:"column:kyt_state"`          // KYT状态 0:待KYT检查 1:自动通过 2:自动拒绝 3:待人工审批 4:人工审批通过 5:人工审批拒绝
	RiskAddressScore string                       `gorm:"column:risk_address_score"` // 地址KYT分数
	RiskHashScore    string                       `gorm:"column:risk_hash_score"`    // 交易hash KYT分数
	VaspType         int64                        `gorm:"column:vasp_type"`          // 是否来自第三方 0:个人 1:第三方
	VaspName         string                       `gorm:"column:vasp_name"`          // 第三方名称
	PassReason       string                       `gorm:"column:pass_reason"`        // 通过原因
	FailReason       string                       `gorm:"column:fail_reason"`        // 失败原因
	CreateTime       time.Time                    `gorm:"column:create_time"`        // 创建时间
	UpdateTime       time.Time                    `gorm:"column:update_time"`        // 更新时间
	Creator          string                       `gorm:"column:creator"`            // 创建人
	Updater          string                       `gorm:"column:updater"`            // 更新人
	Version          int64                        `gorm:"column:version"`            // 版本号

	ValidateState     fuseCommon.ValidateState `gorm:"column:validate_state"`      // 标记校验状态
	TransactionUnitID int64                    `gorm:"column:transaction_unit_id"` // 关联的TransactionUnit
}

type WithdrawOrderTable struct {
	gorm.Model
	OrderID            int64                         `gorm:"column:order_id"`             // 订单id
	TenantID           int64                         `gorm:"column:tenant_id"`            // 租户id
	ClientID           int64                         `gorm:"column:client_id"`            // 机构id
	UserID             int64                         `gorm:"column:user_id"`              // 用户id
	PortfolioID        int64                         `gorm:"column:portfolio_id"`         // 投资组合id
	WalletOrderID      string                        `gorm:"column:wallet_order_id"`      // 钱包订单id
	WalletID           string                        `gorm:"column:wallet_id"`            // 钱包
	ChainID            string                        `gorm:"column:chain_id"`             // 链名称
	TokenID            string                        `gorm:"column:token_id"`             // 币种
	Currency           string                        `gorm:"column:currency"`             // 币种Code
	ToAddressID        int64                         `gorm:"column:to_address_id"`        // 提币地址id
	ToAddress          string                        `gorm:"column:to_address"`           // 提币地址
	FormattedToAddress string                        `gorm:"column:formatted_to_address"` // 格式化后的提币地址
	Memo               string                        `gorm:"column:memo"`                 // memo
	TransactionHash    string                        `gorm:"column:transaction_hash"`     // 交易hash
	TransferIndex      int64                         `gorm:"column:transfer_index"`       // 交易index
	Amount             string                        `gorm:"column:amount"`               // 提币数量
	FeeCurrency        int64                         `gorm:"column:fee_currency"`         // 手续费币种
	Fee                string                        `gorm:"column:fee"`                  // 手续费
	TotalAmount        string                        `gorm:"column:total_amount"`         // 提币总金额（U）
	RiskAddressScore   string                        `gorm:"column:risk_address_score"`   // 地址KYT分数
	KytState           int64                         `gorm:"column:kyt_state"`            // KYT状态
	State              fuseCommon.WithdrawOrderState `gorm:"column:state"`                // 订单状态
	FailReason         string                        `gorm:"column:fail_reason"`          // 失败原因
	StartTime          int64                         `gorm:"column:start_time"`           // 开始时间
	EndTime            int64                         `gorm:"column:end_time"`             // 结束时间
	CreateTime         time.Time                     `gorm:"column:create_time"`          // 创建时间
	UpdateTime         time.Time                     `gorm:"column:update_time"`          // 更新时间
	Creator            string                        `gorm:"column:creator"`              // 创建人
	Updater            string                        `gorm:"column:updater"`              // 更新人
	Version            int64                         `gorm:"column:version"`              // 版本号

	ValidateState     fuseCommon.ValidateState `gorm:"column:validate_state"`      // 标记校验状态
	TransactionUnitID int64                    `gorm:"column:transaction_unit_id"` // 关联的TransactionUnit
}

type TransactionUnitTable struct {
	gorm.Model
	Type            fuseCommon.TransactionType `gorm:"column:type"`             // 类型：充值/提现
	OrderIDs        OrderIDsArray              `gorm:"column:order_ids"`        // 关联的订单IDs
	StatementIDs    StatementIDsArray          `gorm:"column:statement_ids"`    // 关联的流水IDs
	ValidateState   fuseCommon.ValidateState   `gorm:"column:validate_state"`   // 标记校验状态
	HitRuleID       int64                      `gorm:"column:hit_rule_id"`      // 命中规则ID
	WalletID        string                     `gorm:"column:wallet_id"`        // 平台ID
	ChainID         string                     `gorm:"column:chain_id"`         // 链名称
	TokenID         string                     `gorm:"column:token_id"`         // 币种
	TransactionHash string                     `gorm:"column:transaction_hash"` // 交易hash
	TransferIndex   int64                      `gorm:"column:transfer_index"`   // 交易index
	ToAddress       string                     `gorm:"column:to_address"`       // 目标地址
	Memo            string                     `gorm:"column:memo"`             // memo
}

func (t *StatementTable) TableName() string {
	return fuseCommon.StatementTableName
}

func (t *StatementTable) OnUpdateColumns() []string {
	return []string{"refer_id", "block_height", "block_hash", "transaction_hash", "block_timestamp",
		"transfer_index", "from_address", "to_address", "token_id",
		"amount", "user_type_balance", "sys_type_balance", "cold_type_balance", "platform_balance",
		"memo", "business_type", "state", "created_timestamp"}
}

func (t *DepositOrderTable) TableName() string {
	return fuseCommon.OrderDepositTableName
}

func (t *DepositOrderTable) OnUpdateColumns() []string {
	return []string{"tenant_id", "client_id", "portfolio_id",
		"wallet_order_id", "wallet_id", "chain_id", "token_id", "currency",
		"to_address_id", "to_address", "memo", "transaction_hash", "transfer_index", "amount",
		"risk_address_score", "kyt_state", "state", "fail_reason", "vasp_type",
		"vasp_name", "pass_reason", "fail_reason", "start_time", "end_time", "create_time",
		"update_time", "creator", "updater", "version"}
}
func (t *WithdrawOrderTable) TableName() string {
	return fuseCommon.OrderWithdrawTableName
}

func (t *WithdrawOrderTable) OnUpdateColumns() []string {
	return []string{"tenant_id", "client_id", "user_id", "portfolio_id",
		"wallet_order_id", "wallet_id", "chain_id", "token_id", "currency", "to_address_id",
		"to_address", "memo", "transaction_hash", "transfer_index", "amount", "fee_currency", "fee",
		"total_amount", "risk_address_score", "kyt_state", "state",
		"fail_reason", "start_time", "end_time", "create_time", "update_time",
		"creator", "updater", "version"}
}

func (t *TransactionUnitTable) TableName() string {
	return fuseCommon.TransactionUnitTableName
}

type OrderIDsArray []int64
type StatementIDsArray []string

func (o *OrderIDsArray) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o OrderIDsArray) Value() (driver.Value, error) {
	return json.Marshal(o)
}

func (s *StatementIDsArray) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, s)
}

func (s StatementIDsArray) Value() (driver.Value, error) {
	return json.Marshal(s)
}

type QueryOrderFilter struct {
	Page, PerPage      int
	Order              string
	OrderIDs           []int64
	ChainID            string
	TokenID            string
	TransactionHash    string
	TransferIndex      *int64
	ToAddress          string
	FormattedToAddress string
	Memo               string
	TransactionUnitIDs []int64
	ValidateStates     []fuseCommon.ValidateState
	WithdrawStates     []fuseCommon.WithdrawOrderState
	DepositStates      []fuseCommon.DepositOrderState
}

type QueryStatementFilter struct {
	Page, PerPage      int
	Order              string
	WalletID           string
	StatementIDs       []string
	ReferIDs           []string
	ChainID            string
	TokenID            string
	TransactionHash    string
	TransferIndex      *int64
	ToAddress          string
	Memo               string
	TransactionUnitIDs []int64
	BusinessType       fuseCommon.StatementBusinessType
	State              *fuseCommon.StatementState
	ValidateStates     []fuseCommon.ValidateState
}

type QueryTransactionUnitFilter struct {
	Page, PerPage   int
	Order           string
	IDs             []int64
	StatementID     string
	WalletID        string
	OrderID         string
	ChainID         string
	TokenID         string
	TransactionHash string
	TransferIndex   *int64
	ToAddress       string
	Memo            string
	Type            *fuseCommon.TransactionType
	ValidateStates  []fuseCommon.ValidateState
}
