/**
 * @note
 * transaction_unit
 *
 * <AUTHOR>
 * @date 	2025-04-02
 */
package transaction

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	. "gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

// QueryTransactionUnitsBySeveralConditions 根据多个条件查询订单+流水单元
func (m *TransactionModelImpl) QueryTransactionUnitsBySeveralConditions(ctx context.Context, db *gorm.DB,
	filter QueryTransactionUnitFilter) ([]*TransactionUnitTable, error) {
	db = db.Model(&TransactionUnitTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryTransactionUnitConditions(ctx, filter, db)

	tbs := make([]*TransactionUnitTable, 0)
	if filter.Order != StringEmpty {
		db = db.Order(filter.Order)
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

// QueryTransactionUnitCountBySeveralConditions 根据多个条件查询Unit总量
func (m *TransactionModelImpl) QueryTransactionUnitCountBySeveralConditions(ctx context.Context, db *gorm.DB,
	filter QueryTransactionUnitFilter) (count int64, err error) {
	db = db.Model(&TransactionUnitTable{})

	db = assembleQueryTransactionUnitConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

// CreateTransactionUnit 插入订单+流水单元
func (m *TransactionModelImpl) CreateTransactionUnit(ctx context.Context, db *gorm.DB,
	transactionUnit *TransactionUnitTable) (int64, error) {
	db = db.Model(&TransactionUnitTable{})
	id := idgen.GetID()
	transactionUnit.ID = uint(id) // 生成唯一ID
	return id, db.Create(transactionUnit).Error
}

// UpdateTransactionUnitByID 更新订单+流水单元
func (m *TransactionModelImpl) UpdateTransactionUnitByID(ctx context.Context, db *gorm.DB, unitID int64,
	validateState *fuseCommon.ValidateState, hitRuleID *int64, walletID *string,
	statementIDs *StatementIDsArray, orderIDs *OrderIDsArray) (err error) {
	db = db.Model(&TransactionUnitTable{})
	updateMap := make(map[string]interface{})
	if validateState != nil {
		updateMap["validate_state"] = *validateState
	}
	if hitRuleID != nil {
		updateMap["hit_rule_id"] = *hitRuleID
	}
	if statementIDs != nil {
		updateMap["statement_ids"] = statementIDs
	}
	if orderIDs != nil {
		updateMap["order_ids"] = orderIDs
	}
	if walletID != nil {
		updateMap["wallet_id"] = *walletID
	}
	return db.Where("id = ?", unitID).Updates(updateMap).Error
}

func assembleQueryTransactionUnitConditions(ctx context.Context, filter QueryTransactionUnitFilter, db *gorm.DB) *gorm.DB {
	// ID筛选
	if len(filter.IDs) > 0 {
		db = db.Where("id in (?)", filter.IDs)
	}
	// walletID筛选
	if filter.WalletID != StringEmpty {
		db = db.Where("wallet_id = ?", filter.WalletID)
	}
	// statementID筛选
	if filter.StatementID != StringEmpty {
		db = db.Where("JSON_CONTAINS(statement_ids, ?)", fmt.Sprintf(`"%s"`, filter.StatementID))
	}
	// orderID筛选
	if filter.OrderID != StringEmpty {
		db = db.Where("JSON_CONTAINS(order_ids, ?)", fmt.Sprintf("[%s]", filter.OrderID))
	}
	// 链ID
	if filter.ChainID != StringEmpty {
		db = db.Where("chain_id = ?", filter.ChainID)
	}
	// 币种ID
	if filter.TokenID != StringEmpty {
		db = db.Where("token_id = ?", filter.TokenID)
	}
	// 交易Hash
	if filter.TransactionHash != StringEmpty {
		db = db.Where("transaction_hash = ?", filter.TransactionHash)

	}
	// 交易Index
	if filter.TransferIndex != nil {
		db = db.Where("transfer_index = ?", filter.TransferIndex)
	}

	// 交易类型状态
	if filter.Type != nil {
		db = db.Where("type = ?", filter.Type)
	}

	if filter.ToAddress != StringEmpty {
		db = db.Where("to_address = ?", filter.ToAddress)
	}

	if filter.Memo != StringEmpty {
		db = db.Where("memo = ?", filter.Memo)
	}

	// 校验状态
	if len(filter.ValidateStates) > 0 {
		db = db.Where("validate_state in (?)", filter.ValidateStates)
	}
	return db
}
