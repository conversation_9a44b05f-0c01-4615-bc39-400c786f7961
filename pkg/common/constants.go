/**
 * @note
 * constants.go
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"os"
	"time"
)

var (
	Hostname string
)

const (
	ModuleName = "fuse"

	DBName = "fuse"
)

func init() {
	Hostname, _ = os.Hostname()
	Hostname = ModuleName + "_" + Hostname
}

var (
	LocalLocation, _ = time.LoadLocation("Asia/Shanghai")
)

type (
	RuleStatus int64
	FuseStatus int64
	FuseType   int64
	AssetType  int64
	SyncStatus int64

	StatementState     int64
	DepositOrderState  string
	WithdrawOrderState string
	TransactionType    int64

	ValidateState int64

	OrderType string

	StatementBusinessType string
)

const (
	// rule status enum
	RuleStatusDisabled RuleStatus = 0 // 未启用
	RuleStatusEnabled  RuleStatus = 1 // 启用

	// token status enum
	FuseStatusNormal FuseStatus = 0 // 未熔断
	FuseStatusFused  FuseStatus = 1 // 熔断

	// token fuse type
	FuseTypeManual FuseType = 0 // 人工熔断
	FuseTypeAuto   FuseType = 1 // 自动熔断

	// asset type enum
	AssetTypeToken AssetType = 0
	AssetTypeChain AssetType = 1

	// statement state enum
	StatementStateOnchain StatementState = 1 // 上链
	StatementStateRevert  StatementState = 2 // 回滚

	// statement state enum
	SyncStatusOff SyncStatus = 0 // 上链
	SyncStatusOn  SyncStatus = 1 // 回滚

	// statement business type enum
	StatementBusinessTypeDeposit     StatementBusinessType = "deposit"       // 充值。 目标地址是用户地址
	StatementBusinessTypeWithdraw    StatementBusinessType = "withdraw"      // 提币。来源于内部, 目标是外部者用户地址
	StatementBusinessTypeOddDeposit  StatementBusinessType = "odd_deposit"   // 异常充值。 来源于外部，非用户地址的入金
	StatementBusinessTypeInnerOut    StatementBusinessType = "inner_out"     // 内部出金。 零钱整理类
	StatementBusinessTypeInnerIn     StatementBusinessType = "inner_in"      // 内部入金。 零钱整理类
	StatementBusinessTypeFee         StatementBusinessType = "fee"           // 交易费用手续费。无法定位金额流向地址
	StatementBusinessTypeInnerFeeOut StatementBusinessType = "inner_fee_out" // 手续费转出。内部地址间,from特殊地址,且是主币
	StatementBusinessTypeInnerFeeIn  StatementBusinessType = "inner_fee_in"  // 手续费转入。内部地址间,from特殊地址,且是主币

	// deposit order state enum
	DepositOrderStateInitialized     DepositOrderState = "init"       // "初始化"
	DepositOrderStateBlockConfirming DepositOrderState = "confirming" // 区块确认中"
	DepositOrderStateProcessing      DepositOrderState = "processing" // 处理中"
	DepositOrderStateAuditing        DepositOrderState = "auditing"   // 审核中"
	DepositOrderStatePendingAuth     DepositOrderState = "to_auth"    // 待认证"
	DepositOrderStateDepositing      DepositOrderState = "depositing" // 充值中"
	DepositOrderStateSuccess         DepositOrderState = "success"    // 充值成功"
	DepositOrderStateFailing         DepositOrderState = "failing"    // 充值失败中"
	DepositOrderStateFailed          DepositOrderState = "failed"     // 充值失败"
	DepositOrderStateAbnormal        DepositOrderState = "ab_normal"  // 异常失败"
	DepositOrderStateRollback        DepositOrderState = "rollback"   // 充值回滚"

	// withdraw order state enum
	WithdrawOrderStateProcessing  WithdrawOrderState = "processing"  // 处理中
	WithdrawOrderStateAuditing    WithdrawOrderState = "auditing"    // 审核中
	WithdrawOrderStateWithdrawing WithdrawOrderState = "withdrawing" // 提币中
	WithdrawOrderStateSuccess     WithdrawOrderState = "success"     // 提币成功
	WithdrawOrderStateFailing     WithdrawOrderState = "failing"     // 提币失败中
	WithdrawOrderStateFailed      WithdrawOrderState = "failed"      // 提币失败

	TransactionTypeDeposit  TransactionType = 0 // 充值
	TransactionTypeWithdraw TransactionType = 1 // 提现

	ValidateStateToBeValidated ValidateState = 0 // 待校验
	ValidateStatePassed        ValidateState = 1 // 已校验通过
	ValidateStateAbnormal      ValidateState = 2 // 校验异常
	ValidateStateManualPassed  ValidateState = 3 // 已手动调整为校验通过

	OrderTypeWithdraw OrderType = "withdraw"
	OrderTypeDeposit  OrderType = "deposit"

	WalletIDCustomer = "avenir_customer"
	WalletIDCompany  = "avenir_company"
)

// 参数定义，操作符等
const (
	// 支持用户输入的参数名字
	ParamLatency          = "latency"
	ParamWalletID         = "walletID"
	ParamAmountDifference = "amountDifference"
	ParamTransactionType  = "transactionType"

	// 下面是引擎自带的参数，暂时不支持用户使用
	ParamChain = "chain"
	ParamToken = "token"
	ParamStag  = "stag"

	OpIn    = "in"
	OpNotIn = "notIn"

	PoolMinLen = 1
	PoolMaxLen = 16
)

const (
	OperationWebAuthnRegister    = "webAuthnRegister"
	OperationChainCreate         = "chainCreate"
	OperationChainModify         = "chainModify"
	OperationChainDelete         = "chainDelete"
	OperationTokenCreate         = "tokenCreate"
	OperationTokenModify         = "tokenModify"
	OperationTokenDelete         = "tokenDelete"
	OperationPlatformCreate      = "platformCreate"
	OperationPlatformModify      = "platformModify"
	OperationPlatformDelete      = "platformDelete"
	OperationRuleCreate          = "ruleCreate"
	OperationRuleModify          = "ruleModify"
	OperationRuleDelete          = "ruleDelete"
	OperationSetTransactionState = "transactionStateSet"
)

// table name
const (
	UserTableName             = "fuse_user"
	OpLogTableName            = "fuse_operation_log"
	AccessKeyTableName        = "fuse_access_key"
	StatementTableName        = "fuse_statement"        // 同步拉过来的流水信息
	OrderDepositTableName     = "fuse_deposit_order"    // 同步拉过来的充值订单信息
	OrderWithdrawTableName    = "fuse_withdraw_order"   // 同步拉过来的提现订单信息
	TransactionUnitTableName  = "fuse_transaction_unit" // 匹配好的流水+订单信息
	ChainTableName            = "fuse_chain"            // 配置的链信息
	TokenTableName            = "fuse_token"            // 配置的币种信息
	PlatformTableName         = "fuse_platform"         // 配置的平台信息
	FuseRuleTableName         = "fuse_rule"             // 配置的熔断规则信息
	FuseRuleRelationTableName = "fuse_rule_relation"    // 配置的熔断规则与币种的关系表
)

const (
	SyncStatementBatchSize           = 200
	SyncOrderBatchSize               = 200
	MatchStatementBatchSize          = 200
	MatchOrderBatchSize              = 200
	MatchTransactionUnitBatchSize    = 200
	ValidateTransactionUnitBatchSize = 200
)

const (
	ValidatorLeaderKey = "fuse_validator_leader"
	SyncerLeaderKey    = "fuse_syncer_leader"
	LeaderExpire       = 20
	LeaderInterval     = 5
	RefreshInterval    = 15
)

const (
	TokenCacheKeyFormat     = "fuse_cache_token_%s"
	ChainCacheKeyFormat     = "fuse_cache_chain_%s"
	TokenCacheExpireSeconds = 120
	ChainCacheExpireSeconds = 120
)

const (
	UserRoleReadonly = "readonly"
	UserRoleAdmin    = "admin"
)
