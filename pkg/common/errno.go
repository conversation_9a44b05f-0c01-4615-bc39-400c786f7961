/**
 * @note
 * errno
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"gitlab.docsl.com/security/common"
)

// 将特定的errno注入ErrnoDesc
func init() {
	for k, v := range ErrnoDesc {
		common.ErrnoDesc[k] = v
	}
}

const (
	ErrQueryChainList        = 10000
	ErrCreateChain           = 10001
	ErrModifyChain           = 10002
	ErrDeleteChain           = 10003
	ErrQueryTokenList        = 10004
	ErrCreateToken           = 10005
	ErrModifyToken           = 10006
	ErrDeleteToken           = 10007
	ErrSyncToken             = 10008
	ErrQueryAssetsFuseState  = 10009
	ErrQueryAllFuseState     = 10010
	ErrQueryPlatformList     = 10011
	ErrCreatePlatform        = 10012
	ErrModifyPlatform        = 10013
	ErrDeletePlatform        = 10014
	ErrSyncPlatform          = 10015
	ErrQueryOrderList        = 10016
	ErrQueryStatementList    = 10017
	ErrSyncChain             = 10018

	ErrQueryRuleList   = 20000
	ErrQueryRuleDetail = 20001
	ErrCreateRule      = 20002
	ErrModifyRule      = 20003
	ErrDeleteRule      = 20004

	ErrQueryTransactionList        = 30000
	ErrSetTransactionValidateState = 30001
	ErrSetStatementValidateState   = 30002
	ErrSetOrderValidateState       = 30003

	ErrWebAuthnValidateBegin  = 100000
	ErrWebAuthnValidateFinish = 100001
	ErrWebAuthnSignupBegin    = 100002
	ErrWebAuthnSignupFinish   = 100003
)

var ErrnoDesc = map[int]string{
	ErrQueryChainList:        "查询链列表失败",
	ErrCreateChain:           "创建链失败",
	ErrModifyChain:           "修改链失败",
	ErrDeleteChain:           "删除链失败",
	ErrQueryTokenList:        "查询币种列表失败",
	ErrCreateToken:           "创建币种失败",
	ErrModifyToken:           "修改币种失败",
	ErrDeleteToken:           "删除币种失败",
	ErrSyncToken:             "同步币种失败",
	ErrQueryAssetsFuseState:  "查询资产熔断状态失败",
	ErrQueryAllFuseState:     "查询所有熔断资产失败",
	ErrQueryPlatformList:     "查询平台列表失败",
	ErrCreatePlatform:        "创建平台失败",
	ErrModifyPlatform:        "修改平台失败",
	ErrDeletePlatform:        "删除平台失败",
	ErrSyncPlatform:          "同步平台失败",
	ErrQueryOrderList:        "查询订单列表失败",
	ErrQueryStatementList:    "查询流水列表失败",
	ErrSyncChain:             "同步链失败",

	ErrQueryRuleList:   "查询规则列表失败",
	ErrQueryRuleDetail: "查询规则详情失败",
	ErrCreateRule:      "创建规则失败",
	ErrModifyRule:      "修改规则失败",
	ErrDeleteRule:      "删除规则失败",

	ErrQueryTransactionList:        "查询交易列表失败",
	ErrSetTransactionValidateState: "设置交易校验状态失败",
	ErrSetStatementValidateState:   "设置流水校验状态失败",
	ErrSetOrderValidateState:       "设置订单校验状态失败",

	ErrWebAuthnValidateBegin:  "Webauthn令牌校验初始化失败",
	ErrWebAuthnValidateFinish: "Webauthn令牌校验失败",
	ErrWebAuthnSignupBegin:    "Webauthn令牌注册初始化失败",
	ErrWebAuthnSignupFinish:   "Webauthn令牌注册失败",
}
