/**
 * @note
 * aws_test
 *
 * <AUTHOR>
 * @date 	2025-05-08
 */
package aws_v4

import (
	"bytes"
	"fmt"
	"io"

	"net/http"
	"testing"
)

func Test_AwsV4(t *testing.T) {
	body := new(bytes.Buffer)
	body.WriteString(`{"chain_id":"bch","token_id":"bch_bch"}`)
	req, err := http.NewRequest("POST", "https://fuse-open-api-pre.sec-test.yorkapp.com/api/fuse/state", body)
	if err != nil {
		panic(err)
	}
	_, err = SignRequestWithAwsV4UseQueryString(req, &Key{AccessKey: "AKIAIOSFODNN7EXAMPLE", SecretKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"}, "", "fuse")
	if err != nil {
		panic(err)
	}
	fmt.Println("req:")
	fmt.Println(req.URL.String())
	resp, err := http.DefaultTransport.RoundTrip(req)
	if err != nil {
		panic(err)
	}
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	fmt.Println("resp:")
	fmt.Println(string(respBody))
}
