package aws_v4

import (
	"encoding/hex"
	"time"

	"github.com/kataras/iris/v12"

	"gitlab.docsl.com/security/common"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	akModel "gitlab.docsl.com/security/fuse/pkg/model/access_key"
)

// CheckRequestWithAwsV4 runs for server
func CheckRequestWithAwsV4(region, name string) iris.Handler {
	return func(ctx iris.Context) {
		req := ctx.Request()
		a, err := NewAuthorization(req)
		if err != nil {
			renderErrorAndAbort(ctx, common.ErrCodeInterSignature, err)
			return
		}
		ak, err := akModel.QueryAccessKeyByKey(ctx, a.AccessKeyID)
		if err != nil {
			renderErrorAndAbort(ctx, common.ErrCodeInterSignature, err)
			return
		}
		key := &Key{AccessKey: ak.AccessKeyID, SecretKey: ak.AccessSecret}
		var t time.Time
		t, err = a.Check(req, region, name)
		if err != nil {
			renderErrorAndAbort(ctx, common.ErrCodeInterSignature, err)
			return
		}

		sp := new(SignProcess)
		sp.Key = key.Sign(t, region, name)

		writeStringToSign(t, req, a, sp, true, region, name)
		result := hex.EncodeToString(sp.AllSHA256)

		if a.Signature != result {
			renderErrorAndAbort(ctx, common.ErrCodeInterSignature, fuseCommon.ErrAwsV4SignatureNotMatched)
			return
		}
		ctx.Next()
		return
	}
}

func renderErrorAndAbort(ctx iris.Context, errCode int, err ...interface{}) {
	ctx.StopExecution()
	common.SetRet(ctx, common.NewError(errCode, err...))
	return
}
