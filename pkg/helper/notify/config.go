/**
 * @note
 * config
 *
 * <AUTHOR>
 * @date 	2025-04-29
 */
package notify

import (
	"gitlab.docsl.com/security/common"
	"sync"

	"gitlab.docsl.com/security/common_helper/notify"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

var (
	config Config
)

var (
	notificationManagers = make(map[string]*notify.NotificationManager)
	mu                   sync.RWMutex
)

type Config map[string]NotifyConfig

type NotifyConfig struct {
	TeamsWebhookUrl  string
	FeishuWebhookUrl string
	Interval         int64
	SmsNumbers       []string
}

func SetupConfig(c Config) {
	config = c
}

func GetConfig() Config {
	return config
}

func GetNotifyManager(name string) (*notify.NotificationManager, error) {
	mu.RLock()
	if ret, ok := notificationManagers[name]; ok {
		mu.RUnlock()
		return ret, nil
	}
	mu.RUnlock()
	mu.Lock()
	defer mu.Unlock()
	if conf, ok := config[name]; !ok {
		return nil, fuseCommon.ErrInvalidNotifyGroup(name)
	} else {
		manager := notify.CreateNotificationManager()
		if conf.TeamsWebhookUrl != common.StringEmpty {
			if err := manager.AddNotifierWithConfig(notify.TeamsType, map[string]interface{}{"webhook_url": conf.TeamsWebhookUrl}); err != nil {
				return nil, err
			}
		}
		if conf.FeishuWebhookUrl != common.StringEmpty {
			if err := manager.AddNotifierWithConfig(notify.FeishuType, map[string]interface{}{"webhook_url": conf.FeishuWebhookUrl}); err != nil {
				return nil, err
			}
		}
		notificationManagers[name] = manager
		return manager, nil
	}
}
