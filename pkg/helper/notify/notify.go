/**
 * @note
 * notify
 *
 * <AUTHOR>
 * @date 	2025-04-29
 */
package notify

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_server"
	"gitlab.docsl.com/security/common/redis"
	"gitlab.docsl.com/security/common_helper/notify"
	notifyHelper "gitlab.docsl.com/security/common_helper/notify"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

func SendTeamsMessage(ctx context.Context, notifyGroup string, content *notify.NotificationContent, needDeduplication bool) error {
	if conf, ok := config[notifyGroup]; !ok || conf.Interval <= 0 {
		return fuseCommon.ErrInvalidNotifyGroup(notifyGroup)
	} else if conf.TeamsWebhookUrl == common.StringEmpty {
		return nil
	} else if needDeduplication {
		if redisClient, err := redis.DefaultClient(ctx); err == nil {
			redisClient.Printlog(true)
			if suc, err := redisClient.SetNX(fmt.Sprintf(RedisIntervalKeyTeam, notifyGroup, common.MD5Hex(content.Message)), "1", time.Duration(conf.Interval)*time.Second); err == nil {
				if !suc { // 设置不成功，限频
					return nil
				}
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", err)
		}
	}
	manager, err := GetNotifyManager(notifyGroup)
	if err != nil {
		return err
	}
	return manager.Send(notify.TeamsType, content)
}

func SendFeishuMessage(ctx context.Context, notifyGroup string, content *notify.NotificationContent, needDeduplication bool) error {
	if conf, ok := config[notifyGroup]; !ok || conf.Interval <= 0 {
		return fuseCommon.ErrInvalidNotifyGroup(notifyGroup)
	} else if conf.FeishuWebhookUrl == common.StringEmpty {
		return nil
	} else if needDeduplication {
		if redisClient, err := redis.DefaultClient(ctx); err == nil {
			redisClient.Printlog(true)
			if suc, err := redisClient.SetNX(fmt.Sprintf(RedisIntervalKeyFeishu, notifyGroup, common.MD5Hex(content.Message)), "1", time.Duration(conf.Interval)*time.Second); err == nil {
				if !suc { // 设置不成功，限频
					return nil
				}
			}
		} else {
			common.GetLogger(ctx).Errorln("Get redis client err:", err)
		}
	}
	manager, err := GetNotifyManager(notifyGroup)
	if err != nil {
		return err
	}
	return manager.Send(notify.FeishuType, content)
}

func MessageAutoFuse(ctx context.Context, chainIDs, tokenIDs []string, isRecover bool) (err error) {
	var message strings.Builder
	var needSend bool
	if len(chainIDs) > 0 || len(tokenIDs) > 0 {
		message.WriteString(fmt.Sprintf("链：[%s]\n币：[%s]", strings.Join(chainIDs, ","), strings.Join(tokenIDs, ",")))
		needSend = true
	}
	if needSend {
		content := &notifyHelper.NotificationContent{
			Title: func(isRecover bool) string {
				if !isRecover {
					return fmt.Sprintf("自动熔断开启[%s]", http_server.GetConfig().Environment)
				} else {
					return fmt.Sprintf("自动熔断恢复[%s]", http_server.GetConfig().Environment)
				}
			}(isRecover),
			Message: message.String(),
		}
		errTeams := SendTeamsMessage(ctx, NotifyGroupAlarm, content, false)
		if errTeams != nil {
			common.GetLogger(ctx).Errorln("send teams message error:", errTeams)
			err = errTeams
		}
		errFeishu := SendFeishuMessage(ctx, NotifyGroupAlarm, content, false)
		if errFeishu != nil {
			common.GetLogger(ctx).Errorln("send feishu message error:", errFeishu)
			err = errFeishu
		}
	}
	return
}

func MessageManualFuse(ctx context.Context, chainIDs, tokenIDs []string, operator string, isRecover bool) (err error) {
	var message strings.Builder
	var needSend bool
	if len(chainIDs) > 0 || len(tokenIDs) > 0 {
		message.WriteString(fmt.Sprintf("链：[%s]\n币：[%s]\n操作人：[%s]", strings.Join(chainIDs, ","), strings.Join(tokenIDs, ","), operator))
		needSend = true
	}
	if needSend {
		content := &notifyHelper.NotificationContent{
			Title: func(isRecover bool) string {
				if !isRecover {
					return fmt.Sprintf("手动熔断开启[%s]", http_server.GetConfig().Environment)
				} else {
					return fmt.Sprintf("手动熔断恢复[%s]", http_server.GetConfig().Environment)
				}
			}(isRecover),
			Message: message.String(),
		}
		errTeams := SendTeamsMessage(ctx, NotifyGroupAlarm, content, false)
		if errTeams != nil {
			common.GetLogger(ctx).Errorln("send teams message error:", errTeams)
			err = errTeams
		}
		errFeishu := SendFeishuMessage(ctx, NotifyGroupAlarm, content, false)
		if errFeishu != nil {
			common.GetLogger(ctx).Errorln("send feishu message error:", errFeishu)
			err = errFeishu
		}
	}
	return
}
