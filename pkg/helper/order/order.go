/**
 * @note
 * order
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package order

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_client"
)

type DepositOrderResponse struct {
	Code int64             `json:"code"`
	Msg  string            `json:"msg"`
	Data *DepositOrderData `json:"data"`
}
type DepositOrderData struct {
	Total int64               `json:"total"`
	List  []*DepositOrderItem `json:"list"`
}
type DepositOrderItem struct {
	Amount           string   `json:"amount"`           // 充值金额
	ChainID          string   `json:"chainId"`          // 充值链Id
	ClientID         int64    `json:"clientId"`         // 客户id
	Creator          string   `json:"creator"`          // 创建人
	Currency         string   `json:"currency"`         // 充值币种
	EndTime          int64    `json:"endTime"`          // 充值结束时间
	FailReason       string   `json:"failReason"`       // 失败原因
	ID               int64    `json:"id"`               // 订单id
	KytState         string   `json:"kytState"`         // 充值kyt状态
	PassReason       string   `json:"passReason"`       // 通过原因
	PortfolioID      int64    `json:"portfolioId"`      // 投资组合id
	RiskAddressScore string   `json:"riskAddressScore"` // 风险地址分数
	RiskHashScore    string   `json:"riskHashScore"`    // 风险hash分数
	StartTime        int64    `json:"startTime"`        // 充值时间
	State            string   `json:"state"`            // 充值状态
	WalletOrderID    string   `json:"walletOrderId"`    // 钱包订单ID
	WalletID         string   `json:"walletId"`         // 钱包ID
	TenantID         int64    `json:"tenantId"`         // 租户id
	ToAddress        string   `json:"toAddress"`        // 充值地址
	ToAddressID      int64    `json:"toAddressId"`      // 充值地址id
	Memo             string   `json:"memo"`             // memo
	TokenID          string   `json:"tokenId"`          // 充值tokenId
	TransactionHash  string   `json:"transactionHash"`  // 充值hash
	TransactionID    string   `json:"transactionId"`    // 充值订单号
	TransactionIndex string   `json:"transactionIndex"` // 充值交易序号
	Updater          string   `json:"updater"`          // 更新人
	CreateTime       FlexTime `json:"createTime"`       // 创建时间
	UpdateTime       FlexTime `json:"updateTime"`       // 更新时间
	VaspName         string   `json:"vaspName"`         // 第三方名称
	VaspType         int64    `json:"vaspType"`         // 第三方类型
}
type WithdrawOrderResponse struct {
	Code int64              `json:"code"`
	Msg  string             `json:"msg"`
	Data *WithdrawOrderData `json:"data"`
}
type WithdrawOrderData struct {
	Total int64                `json:"total"`
	List  []*WithdrawOrderItem `json:"list"`
}
type WithdrawOrderItem struct {
	Amount             string   `json:"amount"`             // 提币数量
	ChainID            string   `json:"chainId"`            // 链Id
	ClientID           int64    `json:"clientId"`           // 机构id
	Creator            string   `json:"creator"`            // 创建人
	Currency           string   `json:"currency"`           // 币种Code
	EndTime            int64    `json:"endTime"`            // 结束时间
	FailReason         string   `json:"failReason"`         // 失败原因
	Fee                string   `json:"fee"`                // 手续费
	FeeCurrency        int64    `json:"feeCurrency"`        // 手续费币种
	ID                 int64    `json:"id"`                 // 订单id
	KytState           string   `json:"kytState"`           // KYT状态
	PortfolioID        int64    `json:"portfolioId"`        // 投资组合id
	RiskAddressScore   string   `json:"riskAddressScore"`   // 地址KYT分数
	StartTime          int64    `json:"startTime"`          // 开始时间
	State              string   `json:"state"`              // 订单状态
	WalletOrderID      string   `json:"walletOrderId"`      // 钱包订单ID
	WalletID           string   `json:"walletId"`           // 钱包ID
	TenantID           int64    `json:"tenantId"`           // 租户id
	ToAddress          string   `json:"toAddress"`          // 提币地址
	ToFormattedAddress string   `json:"ToFormattedAddress"` // 格式化后的提币地址
	ToAddressID        int64    `json:"toAddressId"`        // 提币地址id
	Memo               string   `json:"memo"`               // memo
	TokenID            string   `json:"tokenId"`            // 资产id
	TotalAmount        string   `json:"totalAmount"`        // 提币总金额（U）
	TransactionHash    string   `json:"transactionHash"`    // 交易hash
	TransactionIndex   string   `json:"transactionIndex"`   // 交易index
	Updater            string   `json:"updater"`            // 更新人
	CreateTime         FlexTime `json:"createTime"`         // 创建时间
	UpdateTime         FlexTime `json:"updateTime"`         // 更新时间
	UserID             int64    `json:"userId"`             // 用户id
}

type OrderListRequest struct {
	PageNum   int64 `json:"pageNum"`
	PageSize  int64 `json:"pageSize"`
	StartTime int64 `json:"startTime,omitempty"`
	EndTime   int64 `json:"endTime,omitempty"`
}

func GetDepositOrderList(ctx context.Context, host string, pageNum, pageSize, startTime,
	endTime int64) ([]*DepositOrderItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()

	path, err := url.JoinPath(host, "/dw-inner/saferDwOrder/deposit/getPage")
	if err != nil {
		return nil, err
	}

	// 构建请求体
	queryParams := map[string]string{
		"pageNum":   fmt.Sprintf("%d", pageNum),
		"pageSize":  fmt.Sprintf("%d", pageSize),
		"startTime": fmt.Sprintf("%d", startTime),
		"endTime":   fmt.Sprintf("%d", endTime),
	}

	// 发送请求
	resp, err := req.SetQueryParams(queryParams).SetResult(&DepositOrderResponse{}).Get(path)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	// 处理响应
	response := resp.Result().(*DepositOrderResponse)
	if response.Code != 0 {
		return nil, common.ErrBizCodeAndMsgf(response.Code, response.Msg)
	} else if response.Data == nil {
		return make([]*DepositOrderItem, 0), nil
	}

	return response.Data.List, nil
}
func GetWithdrawOrderList(ctx context.Context, host string, pageNum, pageSize, startTime,
	endTime int64) ([]*WithdrawOrderItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()

	path, err := url.JoinPath(host, "/dw-inner/saferDwOrder/withdraw/getPage")
	if err != nil {
		return nil, err
	}

	// 构建请求体
	queryParams := map[string]string{
		"pageNum":   fmt.Sprintf("%d", pageNum),
		"pageSize":  fmt.Sprintf("%d", pageSize),
		"startTime": fmt.Sprintf("%d", startTime),
		"endTime":   fmt.Sprintf("%d", endTime),
	}

	// 发送请求
	resp, err := req.SetQueryParams(queryParams).SetResult(&WithdrawOrderResponse{}).Get(path)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	// 处理响应
	response := resp.Result().(*WithdrawOrderResponse)
	if response.Code != 0 {
		return nil, common.ErrBizCodeAndMsgf(response.Code, response.Msg)
	} else if response.Data == nil {
		return make([]*WithdrawOrderItem, 0), nil
	}

	return response.Data.List, nil
}
