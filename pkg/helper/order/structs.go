/**
 * @note
 * 为什么会有这个东西？因为px接口的createTime返回一会是string，一会又是数组
 *
 * <AUTHOR>
 * @date 	2025-05-14
 */
package order

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.docsl.com/security/common"
)

// FlexTime 是一个灵活的时间类型，支持多种格式的输入和输出
type FlexTime time.Time

// UnmarshalJSON 实现自定义的JSON解析
func (ft *FlexTime) UnmarshalJSON(data []byte) error {
	var timeStr string
	var timeArray []int
	// 尝试解析字符串格式的时间
	if err := json.Unmarshal(data, &timeStr); err == nil {
		// 尝试多种时间格式
		formats := []string{
			"2006-01-02 15:04:05",
		}

		for _, format := range formats {
			if t, err := time.ParseInLocation(format, timeStr, common.LocalLocation); err == nil {
				*ft = FlexTime(t)
				return nil
			}
		}
		// 尝试解析数组格式的时间 [year, month, day, hour, minute, second]
	} else if err := json.Unmarshal(data, &timeArray); err == nil {
		if len(timeArray) >= 3 {
			for len(timeArray) < 6 {
				timeArray = append(timeArray, 0)
			}
			t := time.Date(
				timeArray[0],             // 年
				time.Month(timeArray[1]), // 月
				timeArray[2],             // 日
				timeArray[3],             // 时
				timeArray[4],             // 分
				timeArray[5],             // 秒
				0,                        // 纳秒
				common.LocalLocation,     // 时区
			)
			*ft = FlexTime(t)
			return nil
		}
	}

	return fmt.Errorf("unsupport time format: %s", string(data))
}

// MarshalJSON 实现自定义的JSON序列化
func (ft FlexTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ft)
	// 返回标准格式的时间字符串
	return json.Marshal(t.Format("2006-01-02 15:04:05"))
}

// String 返回时间的字符串表示
func (ft FlexTime) String() string {
	t := time.Time(ft)
	return t.Format("2006-01-02 15:04:05")
}

// Time 将FlexTime转换为time.Time
func (ft FlexTime) Time() time.Time {
	return time.Time(ft)
}

// FromTime 从time.Time创建FlexTime
func FromTime(t time.Time) FlexTime {
	return FlexTime(t)
}
