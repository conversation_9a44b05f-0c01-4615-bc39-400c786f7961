/**
 * @note
 * format_address
 *
 * <AUTHOR>
 * @date 	2025-05-25
 */
package wallet

import (
	"context"
	"crypto/tls"
	"net/http"
	"net/url"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_client"
)

type FormatAddressResponse struct {
	Data FormatAddressData `json:"data"`
}

type FormatAddressData struct {
	FormatSuccess bool   `json:"formatSuccess"`
	FormatAddress string `json:"formatAddress"`
}

// FormatAddress 格式化地址
func FormatAddress(ctx context.Context, chainID, address string) (*FormatAddressData, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()

	// 构建基础URL
	baseURL, err := url.Parse(GetConfig().Host)
	if err != nil {
		return nil, err
	}

	// 添加路径
	path, err := url.JoinPath(baseURL.Path, "/wallets/format_address")
	if err != nil {
		return nil, err
	}
	baseURL.Path = path

	// 添加查询参数
	q := baseURL.Query()
	q.Set("chain_id", chainID)
	q.Set("address", address)
	baseURL.RawQuery = q.Encode()

	u := baseURL.String()

	// 生成签名
	signHeaders, err := GenerateSignature(ctx, GetConfig().ApiKey, GetConfig().PrivateKey, http.MethodGet, u, common.StringEmpty)
	if err != nil {
		return nil, err
	}

	// 发送请求
	resp, err := req.SetHeaders(signHeaders).
		SetResult(&FormatAddressResponse{}).Get(u)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	formatAddressResp := resp.Result().(*FormatAddressResponse)
	return &formatAddressResp.Data, nil
}
