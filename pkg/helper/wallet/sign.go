/**
 * @note
 * sign
 *
 * <AUTHOR>
 * @date 	2025-04-22
 */
package wallet

import (
	"context"
	"crypto/ed25519"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/url"
	"strings"
	"time"
)

func GenerateSignature(ctx context.Context, apiKey, apiSecret, method, rawUrl string, requestBody string) (headers map[string]string, err error) {
	// 解码私钥
	privateKey, err := hex.DecodeString(apiSecret)
	if err != nil {
		return nil, err
	}

	u, err := url.Parse(rawUrl)
	if err != nil {
		return nil, err
	}
	// 构造请求体
	timestamp := fmt.Sprintf("%d", time.Now().UnixMilli())

	message := fmt.Sprintf("%s|%s|%s|%s|%s", strings.ToUpper(method), u.Path, timestamp, u.Query().Encode(), requestBody)

	// 计算两次 SHA256 哈希
	hash1 := sha256.Sum256([]byte(message))
	hash2 := sha256.Sum256(hash1[:])
	doubleHash := hash2[:]

	// 使用私钥签名
	signature := ed25519.Sign(ed25519.NewKeyFromSeed(privateKey), doubleHash)
	signatureHex := hex.EncodeToString(signature)

	// 设置请求头
	headers = make(map[string]string)
	headers["X-Api-Signature"] = signatureHex
	headers["X-Api-Timestamp"] = timestamp
	headers["X-Api-Key"] = apiKey

	return headers, nil
}
