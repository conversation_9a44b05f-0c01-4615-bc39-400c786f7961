/**
 * @note
 * jen<PERSON>
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package wallet

import (
	"context"
	"crypto/tls"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	"net/http"
	"net/url"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_client"
)

type StatementItem struct {
	ID               string                           `json:"id"`
	ReferID          string                           `json:"refer_id"`
	BlockHeight      string                           `json:"block_height"`
	BlockHash        string                           `json:"block_hash"`
	TransactionHash  string                           `json:"transaction_hash"`
	BlockTimestamp   string                           `json:"block_timestamp"`
	TransferIndex    int64                            `json:"transfer_index"`
	FromAddress      string                           `json:"from_address"`
	ToAddress        string                           `json:"to_address"`
	WalletID         string                           `json:"wallet_id"`
	ChainID          string                           `json:"chain_id"`
	TokenID          string                           `json:"token_id"`
	Amount           string                           `json:"amount"`
	UserTypeBalance  string                           `json:"user_type_balance"`
	SysTypeBalance   string                           `json:"sys_type_balance"`
	ColdTypeBalance  string                           `json:"cold_type_balance"`
	PlatformBalance  string                           `json:"platform_balance"`
	Memo             string                           `json:"memo"`
	BusinessType     fuseCommon.StatementBusinessType `json:"business_type"`
	State            fuseCommon.StatementState        `json:"state"`
	CreatedTimestamp string                           `json:"created_timestamp"`
}

type StatementListResp struct {
	Data []*StatementItem `json:"data"`
}

type StatementListRequest struct {
	ChainID   string `json:"chain_id"`
	WalletID  string `json:"wallet_id"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	PageNum   int64  `json:"page_num"`
	PageSize  int64  `json:"page_size"`
}

func GetStatementList(ctx context.Context, host, apiKey, apiSecret string, pageNum, pageSize int64,
	chainID, walletID, startTime, endTime string) ([]*StatementItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()
	u, err := url.JoinPath(host, "/statements/history")
	if err != nil {
		return nil, err
	}
	bodyParams := &StatementListRequest{
		ChainID:   chainID,
		WalletID:  walletID,
		StartTime: startTime,
		EndTime:   endTime,
		PageNum:   pageNum,
		PageSize:  pageSize,
	}
	body, err := common.JsonStringEncode(bodyParams)
	if err != nil {
		return nil, err
	}
	signHeaders, err := GenerateSignature(ctx, apiKey, apiSecret, http.MethodPost, u, body)
	if err != nil {
		return nil, err
	}
	resp, err := req.SetHeaders(signHeaders).
		SetResult(&StatementListResp{}).SetBody(bodyParams).Post(u)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}
	jobListResp := resp.Result().(*StatementListResp)
	return jobListResp.Data, nil
}

type WalletListResponse struct {
	Data []*WalletItem `json:"data"`
}

type WalletItem struct {
	Name     string `json:"name"`
	OrgID    string `json:"org_id"`
	WalletID string `json:"wallet_id"`
}

// GET /wallets，没有入参
func GetWalletList(ctx context.Context) ([]*WalletItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()
	u, err := url.JoinPath(GetConfig().Host, "/wallets")
	if err != nil {
		return nil, err
	}

	signHeaders, err := GenerateSignature(ctx, GetConfig().ApiKey, GetConfig().PrivateKey, http.MethodGet, u, "")
	if err != nil {
		return nil, err
	}

	resp, err := req.SetHeaders(signHeaders).
		SetResult(&WalletListResponse{}).Get(u)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	walletListResp := resp.Result().(*WalletListResponse)
	return walletListResp.Data, nil
}

type ChainListResponse struct {
	Data []*ChainItem `json:"data"`
}

type ChainItem struct {
	AvgNewblockTimeMS int64 `json:"avg_newblock_time_ms"`
	// ETH
	ChainID             string `json:"chain_id"`
	ConfirmingThreshold int64  `json:"confirming_threshold"`
	ExplorerURL         string `json:"explorer_url"`
	Protocol            string `json:"protocol"`
	RequireDepositMemo  bool   `json:"require_deposit_memo"`
	RequireWithdrawMemo bool   `json:"require_withdraw_memo"`
	Symbol              string `json:"symbol"`
}

// GET /wallets/chains，没有入参
func GetSupportedChains(ctx context.Context) ([]*ChainItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()
	u, err := url.JoinPath(GetConfig().Host, "/wallets/chains")
	if err != nil {
		return nil, err
	}

	signHeaders, err := GenerateSignature(ctx, GetConfig().ApiKey, GetConfig().PrivateKey, http.MethodGet, u, common.StringEmpty)
	if err != nil {
		return nil, err
	}

	resp, err := req.SetHeaders(signHeaders).
		SetResult(&ChainListResponse{}).Get(u)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	chainListResp := resp.Result().(*ChainListResponse)
	return chainListResp.Data, nil
}

type TokenListResponse struct {
	Data []*TokenItem `json:"data"`
}

// wallets.v1.TokenData
type TokenItem struct {
	ChainID                  string `json:"chain_id"`
	Decimals                 int64  `json:"decimals"`
	FeeTokenID               string `json:"fee_token_id"`
	MaximumWithdrawThreshold string `json:"maximum_withdraw_threshold"`
	MinimumDepositThreshold  string `json:"minimum_deposit_threshold"`
	MinimumWithdrawThreshold string `json:"minimum_withdraw_threshold"`
	Name                     string `json:"name"`
	Symbol                   string `json:"symbol"`
	TokenAddress             string `json:"token_address"`
	TokenID                  string `json:"token_id"`
	Type                     string `json:"type"`
}

// GET /wallets/tokens?chain_id=${chain_id}
func GetSupportedTokens(ctx context.Context, chainID string) ([]*TokenItem, error) {
	client := http_client.NewHttpClient(common.GetLogger(ctx), true).
		SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	req := client.R()

	baseURL, err := url.Parse(GetConfig().Host)
	if err != nil {
		return nil, err
	}

	path, err := url.JoinPath(baseURL.Path, "/wallets/tokens")
	if err != nil {
		return nil, err
	}

	baseURL.Path = path

	// 添加查询参数
	q := baseURL.Query()
	q.Set("chain_id", chainID)
	baseURL.RawQuery = q.Encode()

	u := baseURL.String()

	signHeaders, err := GenerateSignature(ctx, GetConfig().ApiKey, GetConfig().PrivateKey, http.MethodGet, u, common.StringEmpty)
	if err != nil {
		return nil, err
	}

	resp, err := req.SetHeaders(signHeaders).
		SetResult(&TokenListResponse{}).Get(u)
	if err != nil {
		return nil, err
	} else if resp.StatusCode() != http.StatusOK {
		return nil, common.ErrHttpResponsef(resp.Status())
	}

	tokenListResp := resp.Result().(*TokenListResponse)
	return tokenListResp.Data, nil
}
