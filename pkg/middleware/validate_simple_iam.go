/**
 * @note
 * validate_simple_iam
 *
 * <AUTHOR>
 * @date 	2025-05-28
 */
package middleware

import (
	"github.com/kataras/iris/v12"
	"gitlab.docsl.com/security/common"
)

// 简易版校验
func ValidateIAMSimple(roleToPathMap map[string]map[string]bool) iris.Handler {
	return func(ctx iris.Context) {
		user := common.GetUser(ctx)
		if user == nil || user.ID == common.StringEmpty {
			common.SetRet(ctx, common.NewError(common.ErrCodeAuthErr, common.ErrMissingAMandatoryParameterf("user")))
			return
		}
		if pathMap, ok := roleToPathMap[user.Role]; !ok {
			common.SetRet(ctx, common.NewError(common.ErrCodeNoPermission, common.ErrInvalidParameterOptionf(user.Role)))
			return
		} else if !pathMap[ctx.RequestPath(false)] {
			common.SetRet(ctx, common.NewError(common.ErrCodeNoPermission, common.ErrInvalidParameterOptionf(user.Role)))
			return
		}
		ctx.Next()
	}
}
