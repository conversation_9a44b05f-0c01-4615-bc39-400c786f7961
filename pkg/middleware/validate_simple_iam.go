/**
 * @note
 * validate_simple_iam
 *
 * <AUTHOR>
 * @date 	2025-05-28
 */
package middleware

import (
	"errors"
	"github.com/kataras/iris/v12"
	"gitlab.docsl.com/security/common"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	uModel "gitlab.docsl.com/security/fuse/pkg/model/user"
)

// 简易版校验
func ValidateIAMSimple(roleToPathMap map[string]map[string]bool) iris.Handler {
	return func(ctx iris.Context) {
		var role string
		user := common.GetUser(ctx)
		if user == nil || user.ID == common.StringEmpty {
			role = fuseCommon.UserRoleReadonly
		} else {
			ut, err := uModel.GetUserByUserID(ctx, user.ID)
			if errors.Is(err, common.ErrRecordNotFound) { // 如果是找不到，先插一条
				err = uModel.CreateUser(ctx, user.ID, user.Name,
					user.DisplayN<PERSON>, user.Owner,
					user.Email, user.Avatar, fuseCommon.UserRoleReadonly, nil)
				if err != nil {
					common.SetRet(ctx, common.NewError(common.ErrCodeAuthErr, err))
					return
				}
				ut, err = uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID) // 重新查询userTable
			}
			if err != nil {
				common.SetRet(ctx, common.NewError(common.ErrCodeAuthErr, err))
				return
			}
			role = ut.Role
		}
		if pathMap, ok := roleToPathMap[role]; !ok {
			common.SetRet(ctx, common.NewError(common.ErrCodeNoPermission, common.ErrInvalidParameterOptionf(role)))
			return
		} else if !pathMap[ctx.RequestPath(false)] {
			common.SetRet(ctx, common.NewError(common.ErrCodeNoPermission, common.ErrInvalidParameterOptionf(role)))
			return
		}
		ctx.Next()
	}
}
