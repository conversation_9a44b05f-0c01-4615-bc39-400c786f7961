/**
 * @note
 * web<PERSON><PERSON>
 *
 * <AUTHOR>
 * @date 	2025-05-14
 */
package middleware

import (
	"github.com/go-webauthn/webauthn/webauthn"
	"github.com/kataras/iris/v12"

	"gitlab.docsl.com/security/common"
	webauthnHelper "gitlab.docsl.com/security/common/webauthn"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
	uModel "gitlab.docsl.com/security/fuse/pkg/model/user"
)

type WebAuthnRequest struct {
	WebAuthn interface{} `json:"webauthn"`
}

var validateWebAuthnMap = map[string]bool{
	"/api/assets/chain/create":    true,
	"/api/assets/chain/modify":    true,
	"/api/assets/chain/delete":    true,
	"/api/assets/chain/sync":      true,
	"/api/assets/token/create":    true,
	"/api/assets/token/modify":    true,
	"/api/assets/token/delete":    true,
	"/api/assets/token/sync":      true,
	"/api/assets/platform/create": true,
	"/api/assets/platform/modify": true,
	"/api/assets/platform/delete": true,
	"/api/rule/create":            true,
	"/api/rule/modify":            true,
	"/api/rule/delete":            true,
	"/api/transaction/setState":   true,
}

func ValidateWebAuthn() iris.Handler {
	return func(ctx iris.Context) {
		if !validateWebAuthnMap[ctx.Request().URL.Path] {
			ctx.Next()
			return
		}
		common.GetLogger(ctx).Infof("path %s, has to validateWebAuthn", ctx.Request().URL.Path)
		ctxUser := common.GetUser(ctx)
		if ctxUser == nil || ctxUser.ID == common.StringEmpty {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, common.ErrMissingAMandatoryParameterf("user")))
			return
		}
		req := &WebAuthnRequest{}
		ctx.RecordRequestBody(true) // 需要多次读取body的时候，设置为true
		if err := ctx.ReadJSON(req); err != nil {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, err))
			return
		}
		userTable, err := uModel.GetUserByUserID(ctx, common.GetUser(ctx).ID)
		if err != nil {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, err))
			return
		}
		var session webauthn.SessionData
		err = common.JsonStringDecode(userTable.WebauthnSession, &session)
		if err != nil {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, err))
			return
		}
		reqBody, err := common.JsonEncode(req.WebAuthn)
		if err != nil {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, err))
			return
		}
		_, err = webauthnHelper.WebAuthnValidateFinish(ctx, userTable, session, reqBody)
		if err != nil {
			common.SetRet(ctx, common.NewError(fuseCommon.ErrWebAuthnValidateFinish, err))
			return
		}
		ctx.Next()
	}
}
