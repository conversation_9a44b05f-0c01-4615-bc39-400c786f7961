/**
 * @note
 * authorized_keys
 *
 * <AUTHOR>
 * @date 	2025-04-16
 */
package access_key

import (
	"context"

	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type AccessKeyTable struct {
	gorm.Model
	Name         string `gorm:"column:name"`          // name
	Type         int64  `gorm:"column:type"`          // type 0：system，1：user
	Desc         string `gorm:"column:desc"`          // desc
	AccessKeyID  string `gorm:"column:access_key_id"` // access_key
	AccessSecret string `gorm:"column:access_secret"` // access_secret
}

func (t *AccessKeyTable) TableName() string {
	return fuseCommon.AccessKeyTableName
}

type AccessKeyModel interface {
	QueryAccessKeyByKey(ctx context.Context, accessKey string) (*AccessKeyTable, error)
	CreateAccessKey(ctx context.Context, name, desc, accessKeyID, accessSecret string) (id int64, err error)
}

var DefaultService AccessKeyModel = &AccessKeyModelImpl{}

type AccessKeyModelImpl struct{}

func (m *AccessKeyModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

func (m *AccessKeyModelImpl) QueryAccessKeyByKey(ctx context.Context, accessKeyID string) (*AccessKeyTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&AccessKeyTable{})
	tb := &AccessKeyTable{}
	db = db.Where("access_key_id = ?", accessKeyID).First(&tb)
	return tb, db.Error
}

func (m *AccessKeyModelImpl) CreateAccessKey(ctx context.Context, name, desc, accessKeyID, accessSecret string) (id int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	id = idgen.GetID()
	tb := &AccessKeyTable{
		Name:         name,
		Desc:         desc,
		AccessKeyID:  accessKeyID,
		AccessSecret: accessSecret,
	}
	tb.ID = uint(id)
	db = db.Create(tb)
	return id, db.Error
}

func QueryAccessKeyByKey(ctx context.Context, accessKeyID string) (*AccessKeyTable, error) {
	return DefaultService.QueryAccessKeyByKey(ctx, accessKeyID)
}

func CreateAccessKey(ctx context.Context, name, desc, accessKeyID, accessSecret string) (id int64, err error) {
	return DefaultService.CreateAccessKey(ctx, name, desc, accessKeyID, accessSecret)
}
