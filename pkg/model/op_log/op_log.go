/**
 * @note
 * op_log
 *
 * <AUTHOR>
 * @date 	2025-03-14
 */
package op_log

import (
	"context"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type OperationLogModel interface {
	// QueryOperationLogsBySeveralConditions 根据多个条件查询Log
	QueryOperationLogsBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) ([]*OpLogTable, error)
	// QueryOperationLogsCountBySeveralConditions 根据多个条件查询Log总量
	QueryOperationLogsCountBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) (int64, error)
	// CreateOperationLog 插入操作记录
	CreateOperationLog(ctx context.Context, userID, userName, operation string, detail *OperationDetail) (err error)
}

var DefaultService OperationLogModel = &OperationLogModelImpl{}

func QueryOperationLogsBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) ([]*OpLogTable, error) {
	return DefaultService.QueryOperationLogsBySeveralConditions(ctx, filter)
}

func QueryOperationLogsCountBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) (count int64, err error) {
	return DefaultService.QueryOperationLogsCountBySeveralConditions(ctx, filter)
}

func CreateOperationLog(ctx context.Context, operation string, detail *OperationDetail) (err error) {
	user := common.GetUser(ctx)
	if user.ID == common.StringEmpty || user.Name == common.StringEmpty {
		return common.ErrMissingAMandatoryParameterf("user")
	}
	return DefaultService.CreateOperationLog(ctx, user.ID, user.Name, operation, detail)
}

type OperationLogModelImpl struct{}

func (m *OperationLogModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

func (m *OperationLogModelImpl) QueryOperationLogsBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) ([]*OpLogTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&OpLogTable{})
	// 分页
	if filter.PerPage > 0 {
		db = db.Limit(filter.PerPage)
	}
	if filter.Page > 0 {
		db = db.Offset((filter.Page - 1) * filter.PerPage)
	}

	db = assembleQueryConditions(ctx, filter, db)

	tbs := make([]*OpLogTable, 0)
	if filter.Order != common.StringEmpty {
		db = db.Order(filter.Order)
	} else {
		db = db.Order("created_at desc")
	}
	db = db.Find(&tbs)
	return tbs, db.Error
}

func (m *OperationLogModelImpl) QueryOperationLogsCountBySeveralConditions(ctx context.Context, filter QueryOpLogFilter) (count int64, err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return 0, err
	}
	db = db.Model(&OpLogTable{})

	db = assembleQueryConditions(ctx, filter, db)
	db = db.Count(&count)
	return count, db.Error
}

func (m *OperationLogModelImpl) CreateOperationLog(ctx context.Context, userID, userName, operation string, detail *OperationDetail) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&OpLogTable{})
	tb := &OpLogTable{
		UserID:          userID,
		UserName:        userName,
		Operation:       operation,
		OperationDetail: detail,
	}
	tb.ID = uint(idgen.GetID()) // 生成唯一ID
	return db.Create(tb).Error
}

func assembleQueryConditions(ctx context.Context, filter QueryOpLogFilter, db *gorm.DB) *gorm.DB {
	// 任务ID筛选
	if filter.UserName != common.StringEmpty {
		db = db.Where("user_name = ?", filter.UserName)
	}

	// 任务ID筛选
	if filter.StartTime != nil {
		db = db.Where("created_at >= ?", filter.StartTime)
	}
	if filter.EndTime != nil {
		db = db.Where("created_at < ?", filter.EndTime)
	}
	return db
}
