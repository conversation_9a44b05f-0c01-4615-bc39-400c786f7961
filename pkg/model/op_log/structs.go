package op_log

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type OpLogTable struct {
	gorm.Model
	UserID          string           `gorm:"column:user_id"`          // casdoor的userID
	UserName        string           `gorm:"column:user_name"`        // casdoor的name
	Operation       string           `gorm:"column:operation"`        // 操作类型
	OperationDetail *OperationDetail `gorm:"column:operation_detail"` // 操作详情，存储具体修改内容
}

func (t *OpLogTable) TableName() string {
	return fuseCommon.OpLogTableName
}

type QueryOpLogFilter struct {
	Page, PerPage int
	UserName      string     `json:"userName"`
	StartTime     *time.Time `json:"startTime"`
	EndTime       *time.Time `json:"endTime"`
	Order         string     `json:"order"`
}

type OperationDetail struct {
	Target interface{} `json:"target,omitempty"`
	Before interface{} `json:"before,omitempty"`
	After  interface{} `json:"after,omitempty"`
}

// 实现 GORM 序列化接口
func (*OperationDetail) GormDataType() string {
	return "json"
}

func (o *OperationDetail) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return fuseCommon.ErrGormScanJsonAssertToBytes
	}
	return json.Unmarshal(bytes, o)
}

func (o *OperationDetail) Value() (driver.Value, error) {
	return json.Marshal(o)
}