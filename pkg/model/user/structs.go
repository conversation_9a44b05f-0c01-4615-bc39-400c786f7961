/**
 * @note
 * user
 *
 * <AUTHOR>
 * @date 	2025-03-03
 */
package user

import (
	"github.com/go-webauthn/webauthn/webauthn"
	"gitlab.docsl.com/security/common"
	"gorm.io/gorm"

	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type UserTable struct {
	gorm.Model
	UserID              string `gorm:"column:user_id"`              // casdoor的userID
	UserName            string `gorm:"column:user_name"`            // casdoor的name
	DisplayName         string `gorm:"column:display_name"`         // casdoor的displayName
	Owner               string `gorm:"column:owner"`                // casdoor的owner
	Email               string `gorm:"column:email"`                // casdoor的email
	Avatar              string `gorm:"column:avatar"`               // casdoor的avatar
	WebauthnCredentials string `gorm:"column:webauthn_credentials"` // 给webauthn用的credentials保存字段
	WebauthnSession     string `gorm:"column:webauthn_session"`     // 给webauthn用的session字段
	Role                string `gorm:"column:role"`                 // 简易的角色字段
}

func (t *UserTable) TableName() string {
	return fuseCommon.UserTableName
}

// WebAuthnID
// implementation of webauthn.User interface
func (t *UserTable) WebAuthnID() []byte {
	return []byte(t.UserID)
}

func (t *UserTable) WebAuthnName() string {
	return t.UserName
}

func (t *UserTable) WebAuthnDisplayName() string {
	return t.DisplayName
}

func (t *UserTable) WebAuthnCredentials() []webauthn.Credential {
	credentials := make([]webauthn.Credential, 0)
	_ = common.JsonStringDecode(t.WebauthnCredentials, &credentials)
	return credentials
}

func (t *UserTable) WebAuthnIcon() string {
	return t.Avatar
}
