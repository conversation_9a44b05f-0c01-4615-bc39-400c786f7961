/**
 * @note
 * user
 *
 * <AUTHOR>
 * @date 	2025-03-03
 */
package user

import (
	"context"
	"github.com/go-webauthn/webauthn/webauthn"
	"gorm.io/gorm"

	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	"gitlab.docsl.com/security/common/mysql"
	fuseCommon "gitlab.docsl.com/security/fuse/pkg/common"
)

type UserModel interface {
	// GetUserByUserID 根据Casdoor的UserID查找用户表
	GetUserByUserID(ctx context.Context, userID string) (*UserTable, error)
	// CreateUser 插入用户记录
	CreateUser(ctx context.Context, userID string, userName, displayName, owner, email, avatar, role string, webauthnCredentials []*webauthn.Credential) (err error)
	// UpdateUser 更新用户记录
	UpdateUser(ctx context.Context, userID string, userName, displayName, owner,
		email, avatar, role *string, webauthnCredentials *[]*webauthn.Credential, sessionData *webauthn.SessionData) (err error)
}

var DefaultService UserModel = &UserModelImpl{}

func GetUserByUserID(ctx context.Context, userID string) (tb *UserTable, err error) {
	return DefaultService.GetUserByUserID(ctx, userID)
}

func CreateUser(ctx context.Context, userID, userName, displayName,
	owner, email, avatar, role string, webauthnCredentials []*webauthn.Credential) (err error) {
	return DefaultService.CreateUser(ctx, userID, userName, displayName, owner, email, avatar, role, webauthnCredentials)
}

func UpdateUser(ctx context.Context, userID string, userName, displayName,
	owner, email, avatar, role *string, webauthnCredentials *[]*webauthn.Credential, sessionData *webauthn.SessionData) (err error) {
	return DefaultService.UpdateUser(ctx, userID, userName, displayName, owner, email, avatar, role, webauthnCredentials, sessionData)
}

type UserModelImpl struct{}

func (m *UserModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(fuseCommon.DBName, false, common.GetLogger(ctx))
}

func (m *UserModelImpl) GetUserByUserID(ctx context.Context, userID string) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("user_id = ?", userID).First(ret)
	return ret, db.Error
}

func (m *UserModelImpl) CreateUser(ctx context.Context, userID, userName, displayName,
	owner, email, avatar, role string, webauthnCredentials []*webauthn.Credential) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserTable{})
	tb := &UserTable{
		UserID:              userID,
		UserName:            userName,
		DisplayName:         displayName,
		Owner:               owner,
		Email:               email,
		Avatar:              avatar,
		WebauthnCredentials: "[]",
		WebauthnSession:     "{}",
		Role:                role,
	}
	if len(webauthnCredentials) > 0 {
		tb.WebauthnCredentials, _ = common.JsonStringEncode(webauthnCredentials)
	}
	tb.ID = uint(idgen.GetID()) // 生成唯一ID
	return db.Create(tb).Error
}

func (m *UserModelImpl) UpdateUser(ctx context.Context, userID string, userName, displayName,
	owner, email, avatar, role *string, webauthnCredentials *[]*webauthn.Credential, sessionData *webauthn.SessionData) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserTable{})
	updateMap := make(map[string]interface{})
	if userName != nil {
		updateMap["user_name"] = *userName
	}
	if displayName != nil {
		updateMap["display_name"] = *displayName
	}
	if owner != nil {
		updateMap["owner"] = *owner
	}
	if email != nil {
		updateMap["email"] = *email
	}
	if avatar != nil {
		updateMap["avatar"] = *avatar
	}
	if role != nil {
		updateMap["role"] = *role
	}
	if webauthnCredentials != nil {
		webauthnCredentialsStr, _ := common.JsonStringEncode(*webauthnCredentials)
		updateMap["webauthn_credentials"] = webauthnCredentialsStr
	}
	if sessionData != nil {
		webauthnSessionDataStr, _ := common.JsonStringEncode(sessionData)
		updateMap["webauthn_session"] = webauthnSessionDataStr
	}
	return db.Where("user_id = ?", userID).Updates(updateMap).Error
}
